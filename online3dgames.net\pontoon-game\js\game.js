$(document).ready(function() {
    $('body').css('font-family', '-apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", Roboto, "Helvetica Neue", Arial, sans-serif');

    let effectsMuted = false;
    let effectsVolume = 0.5;
    let dealSound = null;

    let debugMode = false;

    window.addEventListener('languageChanged', function(event) {
        updateGameTexts();
    });

    function updateGameTexts() {
        if (window.i18n) {
            const currentStatus = $('#game-status').text();

            if (currentStatus.includes('place your bet') || currentStatus.includes('Setzen Sie')) {
                updateGameStatus(window.i18n.t('ui.gameStatus'));
            } else if (currentStatus.includes('Twist or Stick') || currentStatus.includes('Drehen oder Bleiben')) {
                updateGameStatus(window.i18n.t('messages.yourTurn'));
            } else if (currentStatus.includes('Click Done') || currentStatus.includes('Klicken Sie')) {
                updateGameStatus(window.i18n.t('messages.clickDoneToStart'));
            } else if (currentStatus.includes('Welcome') || currentStatus.includes('Willkommen')) {
                updateGameStatus(window.i18n.t('messages.welcomePlaceBet'));
            } else if (currentStatus.includes('Shuffling') || currentStatus.includes('gemischt')) {
                updateGameStatus(window.i18n.t('messages.shuffling'));
            } else if (currentStatus.includes('Calculating') || currentStatus.includes('berechnet')) {
                updateGameStatus(window.i18n.t('messages.calculatingResults'));
            }
        }
    }

    try {
        dealSound = new Audio('/pontoon-game/audio/deal.mp3');
        dealSound.preload = 'auto';
        dealSound.volume = effectsVolume;
    } catch (error) {
        console.warn('Failed to load audio:', error);
    }

    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover');
    }

    const safeAreaCSS = `
        <style id="safe-area-styles">
            /* Full screen coverage including notch areas */
            html {
                width: 100vw;
                height: 100vh;
                height: 100dvh; /* Dynamic viewport height for mobile */
                overflow-x: hidden;
                margin: 0;
                padding: 0;
            }

            body {
                min-height: 100vh;
                min-height: 100dvh;
                width: 100vw;
                overflow-x: hidden;
                margin: 0;
                padding: 0;
                /* Force background to extend to all edges including notch areas */
                background-attachment: fixed;
                background-size: cover !important;
                background-position: center center !important;
                /* Ensure body covers entire viewport including unsafe areas */
                position: relative;
            }

            /* Force full coverage on iOS Safari */
            @media screen and (-webkit-min-device-pixel-ratio: 2) {
                html, body {
                    width: 100vw;
                    min-height: 100vh;
                    min-height: 100dvh;
                    margin: 0 !important;
                    padding: 0 !important;
                }
            }

            /* Safe area support for notch and punch-hole displays */
            @supports (padding: max(0px)) {
                .game-container {
                    /* Add padding for safe areas to main content */
                    padding-left: max(8px, env(safe-area-inset-left));
                    padding-right: max(8px, env(safe-area-inset-right));
                    padding-top: max(8px, env(safe-area-inset-top));
                    padding-bottom: max(8px, env(safe-area-inset-bottom));
                }

                .game-controls {
                    /* Ensure controls respect safe areas */
                    padding-left: max(8px, env(safe-area-inset-left));
                    padding-right: max(8px, env(safe-area-inset-right));
                }

                .top-controls {
                    /* Top controls with safe area padding */
                    padding-left: max(8px, env(safe-area-inset-left));
                    padding-right: max(8px, env(safe-area-inset-right));
                    padding-top: max(8px, env(safe-area-inset-top));
                }
            }

            /* Fallback for older browsers */
            @media screen and (max-width: 430px) {
                .game-container {
                    padding-left: 8px;
                    padding-right: 8px;
                }

                .top-controls {
                    padding-left: 8px;
                    padding-right: 8px;
                    padding-top: 8px;
                }
            }

            /* Additional mobile optimizations */
            @media screen and (orientation: landscape) and (max-height: 500px) {
                /* Landscape mode on phones - ensure full coverage */
                html, body {
                    width: 100vw !important;
                    height: 100vh !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    overflow: hidden !important;
                }

                .game-container {
                    width: 100vw;
                    height: 100vh;
                    padding: 0;
                }
            }

            /* iOS specific fixes */
            @supports (-webkit-touch-callout: none) {
                /* iOS Safari specific */
                html {
                    -webkit-text-size-adjust: 100%;
                    -webkit-touch-callout: none;
                    -webkit-user-select: none;
                }

                body {
                    -webkit-overflow-scrolling: touch;
                    /* Prevent iOS Safari from adding margins */
                    margin: 0 !important;
                    padding: 0 !important;
                }
            }
        </style>
    `;

    $('head').append(safeAreaCSS);

    function loadAudioSettings() {
        try {
            const savedEffectsVolume = localStorage.getItem('blackjack-effects-volume');
            const savedEffectsMuted = localStorage.getItem('blackjack-effects-muted');

            if (savedEffectsVolume !== null) {
                effectsVolume = parseFloat(savedEffectsVolume);
            }
            if (savedEffectsMuted !== null) {
                effectsMuted = savedEffectsMuted === 'true';
            }

            updateAudioVolumes();
        } catch (error) {
            console.warn('Failed to load audio settings:', error);
        }
    }

    function updateAudioVolumes() {
        if (dealSound) {
            dealSound.volume = effectsMuted ? 0 : effectsVolume;
        }
    }

    loadAudioSettings();

    const domCache = {
        $body: $('body'),
        $document: $(document),
        $window: $(window),
        $gameStatus: null,
        $bettingControls: null,
        $actionControls: null,
        $dealCards: null,
        $balance: null,
        $deckCount: null,
    };
    const activeTimers = {
        timeouts: new Set(),
        intervals: new Set(),
        animationFrames: new Set()
    };
    function safeSetTimeout(callback, delay) {
        const timeoutId = setTimeout(() => {
            activeTimers.timeouts.delete(timeoutId);
            callback();
        }, delay);
        activeTimers.timeouts.add(timeoutId);
        return timeoutId;
    }
    function safeClearInterval(intervalId) {
        if (intervalId) {
            clearInterval(intervalId);
            activeTimers.intervals.delete(intervalId);
        }
    }
    function clearAllTimers() {
        activeTimers.timeouts.forEach(id => clearTimeout(id));
        activeTimers.intervals.forEach(id => clearInterval(id));
        activeTimers.animationFrames.forEach(id => cancelAnimationFrame(id));
        activeTimers.timeouts.clear();
        activeTimers.intervals.clear();
        activeTimers.animationFrames.clear();
    }
    function cleanupAnimationElements() {
        $('.flying-chip').remove();
        $('[class*="animate-"]').removeClass(function(index, className) {
            return (className.match(/(^|\s)animate-\S+/g) || []).join(' ');
        });
    }
    function initDOMCache() {
        domCache.$gameStatus = $('#game-status');
        domCache.$bettingControls = $('#betting-controls');
        domCache.$actionControls = $('#action-controls');
        domCache.$dealCards = $('#deal-cards');
        domCache.$deckCount = $('#deck-count');

        domCache.$rulesModal = $('#rules-modal');
        domCache.$sideBetControls = $('#side-bet-controls');
    }
    let gameSettings = {
        playerCount: 1,
        deckCount: 6,
        pendingDeckCount: null
    };

    function getPlayerHtmlPosition(playerIndex) {
        if (gameSettings.playerCount === 1) {
            return 0;
        } else if (gameSettings.playerCount === 3) {
            return [0, 1, 2][playerIndex];
        } else if (gameSettings.playerCount === 4) {
            return [0, 1, 2, 3][playerIndex];
        } else if (gameSettings.playerCount === 6) {
            return [0, 1, 2, 3, 4, 5][playerIndex];
        }
        return playerIndex;
    }
    let gameState = {
        deck: [],
        discardPile: [],
        totalCards: 0,
        dealerCards: [],
        dealerScore: 0,
        players: [],
        currentPlayerIndex: 0,
        balance: 1000,
        balanceCache: 1000,
        gameInProgress: false,
        dealerHiddenCard: null,
        gameHistory: [],
        soundEnabled: true,
        currentTurnIndex: -1,
        gamePhase: 'waiting',
        gameStarted: false,
        isShuffling: false,
        dealerHasBlackjack: false,
        hasAutoFullscreened: false

    };
    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const suitColors = {'♠': 'black', '♣': 'black', '♥': 'red', '♦': 'red'};
    function initializePlayers() {
        gameState.players = [];
        const humanPlayerIndex = 0; 
        for (let i = 0; i < gameSettings.playerCount; i++) {
            const isHuman = i === humanPlayerIndex;
            const player = {
                cards: [],
                score: 0,
                bet: 0,
                isActive: false,
                isAI: !isHuman,
                name: 'You',
                isBust: false,
                avatar: '/pontoon-game/images/user.png',
                splitHands: [],
                currentHandIndex: 0,
                canSplit: false,
            };
            gameState.players.push(player);
        }
        gameState.currentPlayerIndex = humanPlayerIndex;
    }
    function updatePlayerPositionsDisplay() {
        $('.player-position').hide();
        $('.players-area').removeClass('player-count-1 player-count-3 player-count-4 player-count-6');
        $('.players-area').addClass(`player-count-${gameSettings.playerCount}`);


        for (let i = 0; i < gameSettings.playerCount; i++) {
            const htmlPosition = getPlayerHtmlPosition(i);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            if (playerPosition.length > 0) {
                playerPosition.show();
                const player = gameState.players[i];
                if (player) {
                    const avatarImg = playerPosition.find('.player-avatar img');
                    const playerName = playerPosition.find('.player-name');
                    if (avatarImg.length > 0) {
                        avatarImg.attr('src', player.avatar);
                        avatarImg.attr('alt', player.name);
                    }
                    if (playerName.length > 0) {
                        playerName.text(player.name);
                    }
                    playerPosition.addClass('current-player');
                    const playerBalance = playerPosition.find('.player-balance');
                    if (playerBalance.length > 0) {
                        playerBalance.show();
                    }
                }
            }
        }
    }
    function initGame() {
        loadDeckSettings();
        initializePlayers();
        createDeck();
        shuffleDeck();
        updateDisplay();
        hideAllPlayerScores();
        updateChipDenominations();
        bindEvents();
        startGame();
    }
    function createDeck() {
        gameState.deck = [];
        gameState.discardPile = [];

        for (let deckNum = 0; deckNum < gameSettings.deckCount; deckNum++) {
            for (let suit of suits) {
                for (let value of values) {
                    gameState.deck.push({
                        suit: suit,
                        value: value,
                        color: suitColors[suit]
                    });
                }
            }
        }

        gameState.totalCards = gameState.deck.length;
        console.log(`✅ Deck created: ${gameState.totalCards} cards (${gameSettings.deckCount} deck${gameSettings.deckCount > 1 ? 's' : ''})`);

        checkCardIntegrity('After deck creation');
        updateDeckDisplay();
    }
    function updateDeckDisplay() {
        const remainingCards = gameState.deck.length;
        const discardedCards = gameState.discardPile.length;
        const currentTotalCards = remainingCards + discardedCards;
        const baseTotalCards = gameState.totalCards;

        $('#deck-count').text(remainingCards);
        $('#discard-count').text(discardedCards);

        const deckProgress = baseTotalCards > 0 ? (remainingCards / baseTotalCards) * 100 : 0;
        const discardProgress = baseTotalCards > 0 ? (discardedCards / baseTotalCards) * 100 : 0;

        $('#deck-progress-fill').css('width', `${deckProgress}%`);
        $('#discard-progress-fill').css('width', `${discardProgress}%`);

        const deckCard = $('#main-deck .deck-card');
        if (remainingCards === 0) {
            deckCard.css('opacity', '0.3');
        } else if (remainingCards < baseTotalCards * 0.2) {
            deckCard.css('opacity', '0.6');
        } else {
            deckCard.css('opacity', '1');
        }

        validateAndFixCardCount(currentTotalCards, baseTotalCards, remainingCards, discardedCards);
    }

    function validateAndFixCardCount(currentTotal, baseTotal, remaining, discarded) {
        if (currentTotal !== baseTotal) {
            console.warn(`Card count mismatch detected:`);
            console.warn(`- Current total: ${currentTotal} (Remaining: ${remaining} + Discarded: ${discarded})`);
            console.warn(`- Expected total: ${baseTotal}`);

            const difference = baseTotal - currentTotal;

            if (difference > 0) {
                console.warn(`Missing ${difference} cards - this should not happen in normal gameplay`);
            } else if (difference < 0) {
                console.warn(`Extra ${Math.abs(difference)} cards detected - this should not happen`);
            }

            console.warn('Current game state:', {
                gameInProgress: gameState.gameInProgress,
                gamePhase: gameState.gamePhase,
                deckLength: gameState.deck.length,
                discardPileLength: gameState.discardPile.length,
                dealerCardsCount: gameState.dealerCards.length,
                playersCardsCount: gameState.players.map(p => ({
                    cards: p.cards.length,
                    splitHands: p.splitHands.map(h => h.cards.length)
                }))
            });
        }
    }

    function checkCardIntegrity(context = 'Unknown') {
        let cardsInPlay = 0;

        const deckCards = gameState.deck.length;
        cardsInPlay += deckCards;

        const discardCards = gameState.discardPile.length;
        cardsInPlay += discardCards;

        const dealerCards = gameState.dealerCards.length;
        cardsInPlay += dealerCards;

        let playerCards = 0;
        gameState.players.forEach(player => {
            playerCards += player.cards.length;
            if (player.splitHands && player.splitHands.length > 0) {
                player.splitHands.forEach(hand => {
                    playerCards += hand.cards.length;
                });
            }
        });
        cardsInPlay += playerCards;

        const expectedTotal = gameState.totalCards;
        const isValid = cardsInPlay === expectedTotal;
        const difference = cardsInPlay - expectedTotal;

        if (!isValid) {
            console.error(`🚨 Card integrity check FAILED at ${context}:`);
            console.error(`   Expected: ${expectedTotal} | Actual: ${cardsInPlay} | Difference: ${difference > 0 ? '+' : ''}${difference}`);
            console.error(`   Breakdown: Deck(${deckCards}) + Discard(${discardCards}) + Dealer(${dealerCards}) + Players(${playerCards}) = ${cardsInPlay}`);

            if (playerCards > 0) {
                gameState.players.forEach((player, index) => {
                    const mainCards = player.cards.length;
                    const splitCards = player.splitHands ? player.splitHands.reduce((sum, hand) => sum + hand.cards.length, 0) : 0;
                    if (mainCards > 0 || splitCards > 0) {
                        console.error(`   Player ${index}: Main(${mainCards}) + Split(${splitCards}) = ${mainCards + splitCards}`);
                    }
                });
            }
        } else {
            if (debugMode) {
                console.log(`✅ Card integrity check passed at ${context}: ${cardsInPlay}/${expectedTotal} cards`);
            }
        }

        return isValid;
    }

    function shuffleDeck() {
        const originalDeckSize = gameState.deck.length;
        if (originalDeckSize === 0) {
            console.warn('Attempting to shuffle empty deck');
            return;
        }

        const shufflePasses = 3 + Math.floor(Math.random() * 3);
        for (let pass = 0; pass < shufflePasses; pass++) {
            for (let i = gameState.deck.length - 1; i > 0; i--) {
                let randomValue;
                if (window.crypto && window.crypto.getRandomValues) {
                    const array = new Uint32Array(1);
                    window.crypto.getRandomValues(array);
                    randomValue = array[0] / (0xFFFFFFFF + 1);
                } else {
                    randomValue = Math.random();
                }
                const j = Math.floor(randomValue * (i + 1));
                [gameState.deck[i], gameState.deck[j]] = [gameState.deck[j], gameState.deck[i]];
            }

            if (pass < shufflePasses - 1) {
                riffleShuffle();

                if (gameState.deck.length !== originalDeckSize) {
                    console.error(`Shuffle pass ${pass + 1}: deck size changed from ${originalDeckSize} to ${gameState.deck.length}`);
                    break; 
                }
            }
        }

        if (gameState.deck.length !== originalDeckSize) {
            console.error(`Shuffle completed with wrong deck size: expected ${originalDeckSize}, got ${gameState.deck.length}`);
        }

        updateDeckDisplay();
    }
    function riffleShuffle() {
        const deckSize = gameState.deck.length;
        if (deckSize < 2) return;

        const splitPoint = Math.floor(deckSize / 2) + Math.floor(Math.random() * 6) - 3;
        const leftHalf = gameState.deck.slice(0, splitPoint);
        const rightHalf = gameState.deck.slice(splitPoint);

        const newDeck = [];
        let leftIndex = 0;
        let rightIndex = 0;

        while (leftIndex < leftHalf.length || rightIndex < rightHalf.length) {
            const takeFromLeft = leftIndex < leftHalf.length &&
                (rightIndex >= rightHalf.length || Math.random() < 0.5);
            if (takeFromLeft) {
                newDeck.push(leftHalf[leftIndex++]);
            } else {
                newDeck.push(rightHalf[rightIndex++]);
            }
        }

        if (newDeck.length !== deckSize) {
            console.error(`Riffle shuffle error: deck size changed from ${deckSize} to ${newDeck.length}`);
            return; 
        }

        gameState.deck = newDeck;
    }
    function dealCard() {
        if (gameState.deck.length === 0) {
            triggerShuffleSync();
        }
        const card = gameState.deck.pop();
        if (!card) {
            triggerShuffleSync();
            return gameState.deck.pop();
        }
        updateDeckDisplay();
        return card;
    }
    function triggerShuffleSync() {
        if (gameState.isShuffling) {
            return;
        }
        gameState.isShuffling = true;

        const beforeShuffle = {
            deckCount: gameState.deck.length,
            discardCount: gameState.discardPile.length,
            total: gameState.deck.length + gameState.discardPile.length
        };

        console.log(`Before merge: deck=${gameState.deck.length}, discard=${gameState.discardPile.length}`);
        gameState.deck = [...gameState.deck, ...gameState.discardPile];
        gameState.discardPile = [];
        console.log(`After merge: deck=${gameState.deck.length}, discard=${gameState.discardPile.length}`);
        shuffleDeck();
        console.log(`After shuffle: deck=${gameState.deck.length}, discard=${gameState.discardPile.length}`);

        const afterShuffle = {
            deckCount: gameState.deck.length,
            discardCount: gameState.discardPile.length,
            total: gameState.deck.length + gameState.discardPile.length
        };

        console.log(`Shuffle completed: ${beforeShuffle.deckCount} deck + ${beforeShuffle.discardCount} discard = ${beforeShuffle.total} total cards`);

        if (beforeShuffle.total !== afterShuffle.total) {
            console.error(`Shuffle error: Card count changed from ${beforeShuffle.total} to ${afterShuffle.total}`);
        }

        checkCardIntegrity('After sync shuffle');

        gameState.isShuffling = false;
    }
    function triggerShuffleWithAnimation() {
        if (gameState.isShuffling) {
            return Promise.resolve();
        }
        gameState.isShuffling = true;
        updateGameStatus(window.i18n ? window.i18n.t('messages.shuffling') : 'Shuffling...');
        $('.deck-card').addClass('shuffling');

        const beforeShuffle = {
            deckCount: gameState.deck.length,
            discardCount: gameState.discardPile.length,
            total: gameState.deck.length + gameState.discardPile.length
        };

        return new Promise(resolve => {
            safeSetTimeout(() => {
                console.log(`Before animated merge: deck=${gameState.deck.length}, discard=${gameState.discardPile.length}`);
                gameState.deck = [...gameState.deck, ...gameState.discardPile];
                gameState.discardPile = [];
                console.log(`After animated merge: deck=${gameState.deck.length}, discard=${gameState.discardPile.length}`);
                shuffleDeck();
                console.log(`After animated shuffle: deck=${gameState.deck.length}, discard=${gameState.discardPile.length}`);

                const afterShuffle = {
                    deckCount: gameState.deck.length,
                    discardCount: gameState.discardPile.length,
                    total: gameState.deck.length + gameState.discardPile.length
                };

                console.log(`Animated shuffle completed: ${beforeShuffle.deckCount} deck + ${beforeShuffle.discardCount} discard = ${beforeShuffle.total} total cards`);

                if (beforeShuffle.total !== afterShuffle.total) {
                    console.error(`Animated shuffle error: Card count changed from ${beforeShuffle.total} to ${afterShuffle.total}`);
                }

                checkCardIntegrity('After animated shuffle');

                $('.deck-card').removeClass('shuffling');
                gameState.isShuffling = false;
                updateDeckDisplay();
                resolve();
            }, 2000);
        });
    }
    function calculateScore(cards) {
        if (!cards || !Array.isArray(cards)) {
            return 0;
        }
        let score = 0;
        let aces = 0;
        for (let card of cards) {
            if (!card || !card.value) {
                continue;
            }
            if (card.value === 'A') {
                aces++;
                score += 11;
            } else if (['J', 'Q', 'K'].includes(card.value)) {
                score += 10;
            } else {
                score += parseInt(card.value);
            }
        }
        while (score > 21 && aces > 0) {
            score -= 10;
            aces--;
        }
        return score;
    }
    function getScoreDisplay(cards) {
        if (!cards || !Array.isArray(cards)) {
            return '0';
        }

        const actualScore = calculateScore(cards);

        let hardScore = 0;
        let aces = 0;
        for (let card of cards) {
            if (!card || !card.value) {
                continue;
            }
            if (card.value === 'A') {
                aces++;
                hardScore += 1;
            } else if (['J', 'Q', 'K'].includes(card.value)) {
                hardScore += 10;
            } else {
                hardScore += parseInt(card.value);
            }
        }

        if (aces === 0) {
            return actualScore.toString();
        }

        let softScore = hardScore + 10;
        if (softScore === actualScore && softScore <= 21 && softScore !== hardScore) {
            return `${hardScore}/${softScore}`;
        } else {
            return actualScore.toString();
        }
    }
    function canPlayerSplit(playerIndex) {
        const player = gameState.players[playerIndex];

        if (player.splitHands.length === 0) {
            if (player.cards.length !== 2) {
                return false;
            }
            if (!player.cards[0] || !player.cards[1]) {
                return false;
            }
            const card1Value = player.cards[0].value;
            const card2Value = player.cards[1].value;
            const isSameValue = card1Value === card2Value;
            const hasEnoughBalance = player.isAI || gameState.balance >= player.bet;

            return isSameValue && hasEnoughBalance;
        }
        
        if (player.splitHands.length >= 3) {
            return false;
        }
        
        if (player.currentHandIndex >= player.splitHands.length) {
            return false;
        }
        
        const currentHand = player.splitHands[player.currentHandIndex];
        if (currentHand.cards.length !== 2) {
            return false;
        }
        
        const card1Value = currentHand.cards[0].value;
        const card2Value = currentHand.cards[1].value;
        const isSameValue = card1Value === card2Value;
        const hasEnoughBalance = player.isAI || gameState.balance >= currentHand.bet;
        
        return isSameValue && hasEnoughBalance;
    }
    function splitPlayerHand(playerIndex) {
        const player = gameState.players[playerIndex];
        if (!canPlayerSplit(playerIndex)) {
            return false;
        }
        
        if (player.splitHands.length === 0) {
            if (!player.isAI) {
                if (gameState.balance < player.bet) {
                    updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalanceForSplit') : 'Insufficient balance for split!');
                    return false;
                }
                gameState.balance -= player.bet;
            }
            animateSplitBetChips(player.bet, playerIndex);
            const firstCard = player.cards[0];
            const secondCard = player.cards[1];
            player.splitHands = [
                { cards: [firstCard], score: 0, bet: player.bet, isBust: false, isComplete: false, hasDoubled: false, hasDrawnCard: false },
                { cards: [secondCard], score: 0, bet: player.bet, isBust: false, isComplete: false, hasDoubled: false, hasDrawnCard: false }
            ];
            player.cards = [];
            player.currentHandIndex = 0;
            player.splitHands[0].score = calculateScore(player.splitHands[0].cards);
            player.splitHands[1].score = calculateScore(player.splitHands[1].cards);
            playSound('deal');
            updateDisplay();
            safeSetTimeout(() => {
                updateBetDisplay(playerIndex, player.bet * 2);
            }, 200);
            performSplitAnimation(playerIndex, () => {
                displaySplitHands(playerIndex, -1);
                safeSetTimeout(() => {
                    player.splitHands[0].cards.push(dealCard());
                    player.splitHands[0].score = calculateScore(player.splitHands[0].cards);
                    playSound('deal');
                    displaySplitHands(playerIndex, 1);
                }, 300);
            });
            const totalBetAfterSplit = player.bet * 2;
            const message = window.i18n ?
                window.i18n.t('messages.playerChoseToSplit', { amount: totalBetAfterSplit.toLocaleString() }) :
                `Player chose to split! Total bet: ${totalBetAfterSplit.toLocaleString()} - Hand 1 of 2 - Twist or Stick?`;
            showGameStatus(message);
            return true;
        } else {
            const currentHand = player.splitHands[player.currentHandIndex];
            if (!player.isAI) {
                if (gameState.balance < currentHand.bet) {
                    updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalanceForSplit') : 'Insufficient balance for split!');
                    return false;
                }
                gameState.balance -= currentHand.bet;
            }
            
            const firstCard = currentHand.cards[0];
            const secondCard = currentHand.cards[1];
            const newHand = { cards: [secondCard], score: 0, bet: currentHand.bet, isBust: false, isComplete: false, hasDoubled: false, hasDrawnCard: false };
            
            currentHand.cards = [firstCard];
            currentHand.score = calculateScore(currentHand.cards);
            
            player.splitHands.splice(player.currentHandIndex + 1, 0, newHand);
            newHand.score = calculateScore(newHand.cards);
            
            playSound('deal');
            updateDisplay();
            
            currentHand.cards.push(dealCard());
            currentHand.score = calculateScore(currentHand.cards);
            
            const totalBet = player.splitHands.reduce((sum, hand) => sum + hand.bet, 0);
            safeSetTimeout(() => {
                updateBetDisplay(playerIndex, totalBet);
            }, 200);
            
            const message = window.i18n ?
                window.i18n.t('messages.handTwistOrStick', { handNumber: player.currentHandIndex + 1 }) :
                `Hand split again! Total bet: ${totalBet.toLocaleString()} - Hand ${player.currentHandIndex + 1} of ${player.splitHands.length} - Twist or Stick?`;
            showGameStatus(message);
            return true;
        }
    }
    function moveToNextSplitHand(playerIndex, callback) {
        const player = gameState.players[playerIndex];
        if (player.splitHands.length === 0) {
            if (callback) callback(false);
            return false;
        }
        player.splitHands[player.currentHandIndex].isComplete = true;
        const currentHand = player.splitHands[player.currentHandIndex];
        const delay = currentHand.isBust ? 1000 : 0;
        safeSetTimeout(() => {
            player.currentHandIndex++;
            if (player.currentHandIndex < player.splitHands.length) {
                performSplitHandTransition(playerIndex, () => {
                    displaySplitHands(playerIndex, -1);
                    const nextHand = player.splitHands[player.currentHandIndex];
                    let newCardIndex = -1;
                    if (nextHand.cards.length === 1) {
                        nextHand.cards.push(dealCard());
                        nextHand.score = calculateScore(nextHand.cards);
                        newCardIndex = 1;
                        playSound('deal');
                        displaySplitHands(playerIndex, newCardIndex);
                    }
                    const message = window.i18n ?
                        window.i18n.t('messages.handTwistOrStick', { handNumber: player.currentHandIndex + 1 }) :
                        `Hand ${player.currentHandIndex + 1} of ${player.splitHands.length} - Twist or Stick?`;
                    updateGameStatus(message);
                    enableGameButtons();
                    updateButtonStates();
                    if (callback) callback(true);
                });
                return true;
            } else {
                displaySplitHandsFinal(playerIndex);
                if (callback) callback(false);
                return false;
            }
        }, delay);
        return true;
    }
    function performSplitAnimation(playerIndex, callback) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        const cards = cardsContainer.find('.card');
        if (cards.length >= 2) {
            const secondCard = cards.eq(1);
            cards.removeClass('dealt').css('animation', 'none');
            const animatedCard = secondCard.clone();
            animatedCard.removeClass('dealt');
            animatedCard.addClass('splitting-card');
            animatedCard.css({
                position: 'absolute',
                top: secondCard.position().top,
                left: secondCard.position().left,
                zIndex: 100,
                animation: 'none'
            });
            cardsContainer.append(animatedCard);
            secondCard.css('opacity', '0');
            const tempMiniHand = $('<div class="mini-hand" style="visibility: hidden;"></div>');
            cardsContainer.append(tempMiniHand);
            const miniPosition = tempMiniHand.position();
            const miniTargetLeft = miniPosition.left;
            const miniTargetTop = miniPosition.top;
            tempMiniHand.remove();
            animatedCard.animate({
                left: miniTargetLeft + 'px',
                top: miniTargetTop + 'px'
            }, {
                duration: 300, 
                easing: 'swing',
                step: function(now, fx) {
                    if (fx.prop === 'left') {
                        const progress = Math.abs(now - fx.start) / Math.abs(fx.end - fx.start);
                        const scale = 1 - (progress * 0.5);
                        $(this).css({
                            'transform': `scale(${scale})`,
                            'transform-origin': 'top left'
                        });
                    }
                },
                complete: function() {
                    $(this).css({
                        'transform': 'scale(0.5)',
                        'transform-origin': 'top left',
                        'left': miniTargetLeft + 'px',
                        'top': miniTargetTop + 'px'
                    });
                    safeSetTimeout(() => {
                        animatedCard.remove();
                        callback();
                    }, 50);
                }
            });
        } else {
            safeSetTimeout(callback, 100);
        }
    }
    function performSplitHandTransition(playerIndex, callback) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        const splitContainer = cardsContainer.find('.split-hands-container');
        if (splitContainer.length === 0) {
            safeSetTimeout(callback, 500);
            return;
        }
        splitContainer.addClass('transitioning');
        safeSetTimeout(() => {
            splitContainer.removeClass('transitioning');
            callback();
        }, 500);
    }
    function displaySplitHands(playerIndex, newCardIndex = -1) {
        const player = gameState.players[playerIndex];
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        let splitContainer = cardsContainer.find('.split-hands-container');
        if (splitContainer.length === 0) {
            cardsContainer.empty();
            splitContainer = $('<div class="split-hands-container"></div>');
            cardsContainer.append(splitContainer);
        }
        if (newCardIndex === -1) {
            splitContainer.find('.current-hand, .mini-hand').remove();
        }
        player.splitHands.forEach((hand, handIndex) => {
            if (handIndex === player.currentHandIndex) {
                let currentHandContainer = splitContainer.find('.current-hand');
                if (currentHandContainer.length === 0) {
                    currentHandContainer = $('<div class="current-hand"></div>');
                    splitContainer.append(currentHandContainer);
                }
                if (newCardIndex >= 0 && newCardIndex < hand.cards.length) {
                    const newCard = hand.cards[newCardIndex];
                    if (newCard) {
                        const cardElement = createCardElement(newCard);
                        cardElement.addClass('dealt');
                        currentHandContainer.append(cardElement);
                    }
                } else {
                    currentHandContainer.find('.card').remove(); 
                    hand.cards.forEach((card, cardIndex) => {
                        if (card) {
                            const cardElement = createCardElement(card);
                            cardElement.removeClass('dealt');
                            cardElement.css('animation', 'none');
                            currentHandContainer.append(cardElement);
                        }
                    });
                }
                let currentScore = currentHandContainer.find('.split-hand-score.current-score');
                if (currentScore.length === 0) {
                } else {
                    currentScore.text(getScoreDisplay(hand.cards));
                }
            } else {
                let miniHandContainer = splitContainer.find(`[data-hand="${handIndex}"]`);
                if (miniHandContainer.length === 0) {
                    miniHandContainer = $(`<div class="mini-hand" data-hand="${handIndex}"></div>`);
                    splitContainer.append(miniHandContainer);
                    hand.cards.forEach(card => {
                        if (card) {
                            const miniCard = createCardElement(card);
                            miniCard.addClass('mini-card');
                            miniCard.removeClass('dealt');
                            miniCard.css('animation', 'none');
                            miniHandContainer.append(miniCard);
                        }
                    });
                } else {
                    miniHandContainer.find('.card').remove();
                    hand.cards.forEach(card => {
                        if (card) {
                            const miniCard = createCardElement(card);
                            miniCard.addClass('mini-card');
                            miniCard.removeClass('dealt');
                            miniCard.css('animation', 'none');
                            miniHandContainer.append(miniCard);
                        }
                    });
                    miniHandContainer.find('.mini-score').text(getScoreDisplay(hand.cards));
                }
                miniHandContainer.removeClass('completed bust won lost push blackjack twenty-one');
                if (hand.isComplete) {
                    miniHandContainer.addClass('completed');
                    if (hand.isBust) {
                        miniHandContainer.addClass('bust');
                    }
                    if (hand.effectClass) {
                        miniHandContainer.addClass(hand.effectClass);
                    }
                }
            }
        });
        const currentHand = player.splitHands[player.currentHandIndex];
        const htmlPos = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPos}`).text(`Hand ${player.currentHandIndex + 1}: ${getScoreDisplay(currentHand.cards)}`);
    }
    function displaySplitHandsFinal(playerIndex) {
        const player = gameState.players[playerIndex];
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        cardsContainer.empty();
        const finalContainer = $('<div class="split-hands-final-container"></div>');
        cardsContainer.append(finalContainer);
        player.splitHands.forEach((hand, handIndex) => {
            const handContainer = $(`<div class="final-hand" data-hand="${handIndex}"></div>`);
            finalContainer.append(handContainer);
            const cardsArea = $('<div class="player-cards-area"></div>');
            handContainer.append(cardsArea);
            hand.cards.forEach(card => {
                const cardElement = createCardElement(card);
                cardsArea.append(cardElement);
            });
            if (hand.isBust) {
                handContainer.addClass('bust');
            }
            if (hand.effectClass) {
                handContainer.addClass(hand.effectClass);
            }
        });
        const handScores = player.splitHands.map((hand, index) => 
            `Hand ${index + 1}: ${getScoreDisplay(hand.cards)}`
        ).join(' | ');
        
        const htmlPos = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPos}`).text(handScores);
    }
    function applySplitHandsSettlement(playerIndex, handResults) {
        const player = gameState.players[playerIndex];
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        const handContainers = cardsContainer.find('.final-hand');
        player.splitHands.forEach((hand, handIndex) => {
            const handContainer = handContainers.eq(handIndex);
            if (handContainer.length > 0 && handResults && handResults[handIndex]) {
                handContainer.removeClass('bust won lost push blackjack twenty-one');
                delete hand.effectClass;
                handContainer.addClass(handResults[handIndex]);
            } else {
                console.warn(`Could not find container for hand ${handIndex}`);
            }
        });
    }

    function createCardElement(card, isHidden = false) {
        if (isHidden) {
            return $('<div class="card card-back"></div>');
        }
        if (!card) {
            console.error('Cannot create card element: card is undefined');
            return $('<div class="card error-card">ERROR</div>');
        }
        if (!card.value || !card.suit || !card.color) {
            console.error('Cannot create card element: card properties are missing', card);
            return $('<div class="card error-card">ERROR</div>');
        }
        const cardElement = $(`
            <div class="card ${card.color}">
                <div class="card-corner top-left">
                    <div class="card-value">${card.value}</div>
                    <div class="card-suit">${card.suit}</div>
                </div>
                <div class="card-center">
                    <div class="card-suit-large">${card.suit}</div>
                </div>
                <div class="card-corner bottom-right">
                    <div class="card-value">${card.value}</div>
                    <div class="card-suit">${card.suit}</div>
                </div>
            </div>
        `);
        safeSetTimeout(() => {
            cardElement.addClass('dealt');
        }, 100);
        return cardElement;
    }

    function playSound(soundType) {
        if (effectsMuted) return;

        try {
            switch (soundType) {
                case 'cardDeal':
                case 'cardFlip':
                case 'hit':
                    if (dealSound) {
                        dealSound.volume = effectsVolume;
                        dealSound.currentTime = 0;
                        dealSound.play().catch(error => {
                        });
                    }
                    break;
                case 'chipPlace':
                    break;
                default:
                    break;
            }
        } catch (error) {
        }
    }
    function ensureBalanceIntegrity() {
        if (gameState.balance < 0) {
            console.warn('Balance became negative, resetting to 0');
            gameState.balance = 0;
        }
        checkGameOverCondition();
    }
    function checkGameOverCondition() {
        if (gameState.balance === 0 && !gameState.gameInProgress) {
            const hasAnyBets = gameState.players.some(player => player.bet > 0);
            if (!hasAnyBets) {
                showGameOverModal();
            }
        }
    }
    function showGameOverModal() {
        $('#game-over-modal').fadeIn(300);
    }
    function hideGameOverModal() {
        $('#game-over-modal').fadeOut(300);
    }
    function restartGame() {
        resetBalanceCache();
        gameState.players.forEach(player => {
            player.bet = 0;
            player.cards = [];
            player.score = 0;
            player.sideBets = { perfectPairs: 0, twentyOnePlusThree: 0 };
            player.splitHands = [];
            player.currentSplitIndex = 0;
        });
        gameState.gameInProgress = false;
        gameState.gamePhase = 'waiting';
        gameState.currentTurnIndex = -1;
        gameState.dealerCards = [];
        gameState.dealerScore = 0;
        gameState.dealerHiddenCard = null;
        gameState.playerReady = false;
        gameState.autoStartScheduled = false;
        if (gameState.countdownTimer) {
            safeClearInterval(gameState.countdownTimer);
            gameState.countdownTimer = null;
        }
        gameState.countdownActive = false;
        hideGameOverModal();
        updateDisplay();
        updateChipDenominations();
        updateChipButtonStates();
        $('.dealer-cards-area').empty();
        $('.player-cards-area').empty();
        hideAllPlayerScores();
        $('.bet-circle').removeClass('has-bet');
        $('.bet-amount').text('');
        $('.bet-chips-stack').empty();
        $('.side-bet-spot').removeClass('has-bet');
        $('.side-bet-amount').text('');
        $('.side-bet-chips-stack').empty();
        $('.player-position, .dealer-section').removeClass('won lost bust push blackjack twenty-one');
        $('.chip-section, .betting-section').show();
        $('.game-actions-section').hide();
        updateButtonStates();
        updateGameStatus(window.i18n ? window.i18n.t('messages.welcomeBack') : 'Welcome back! Place your bets to start playing.');
        startBettingCountdown();
    }
    function updateDisplay() {
        ensureBalanceIntegrity();
        $('#player-balance-0').text(gameState.balance);
        if (gameState.dealerHiddenCard && gameState.dealerCards.length > 1) {
            $('#dealer-score').text(getScoreDisplay([gameState.dealerCards[0]]));
        } else {
            $('#dealer-score').text(getScoreDisplay(gameState.dealerCards));
        }
        updateChipButtonStates();
        gameState.players.forEach((player, index) => {
            const displayBet = player.splitHands.length > 0 ? player.bet * 2 : player.bet;
            updateBetDisplay(index, displayBet);
            if (player.splitHands.length > 0) {
            } else {
                const htmlPos = getPlayerHtmlPosition(index);
                $(`#player-score-${htmlPos}`).text(getScoreDisplay(player.cards));
            }
            const htmlPosition = getPlayerHtmlPosition(index);
            const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
            if (player.bet > 0) {
                betCircle.addClass('has-bet');
            } else {
                betCircle.removeClass('has-bet');
            }
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            if (player.isActive) {
                playerPosition.addClass('active');
            } else {
                playerPosition.removeClass('active');
            }
        });


    }
    function updateBetDisplay(playerIndex, totalBet) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        const betAmountElement = $(`#bet-amount-${htmlPosition}`);
        betCircle.find('.bet-chips-stack').remove();
        if (totalBet === 0) {
            betAmountElement.text('0');
            return;
        }
        const formattedAmount = formatBetAmount(totalBet);
        betAmountElement.text(formattedAmount);
        const chipStack = $('<div class="bet-chips-stack"></div>');
        const chips = calculateChipBreakdown(totalBet);
        const currentDenominations = $('.chip-btn').map(function() {
            return parseInt($(this).data('value'));
        }).get().sort((a, b) => a - b);
        const denominations = currentDenominations.length > 0 ? currentDenominations : generateChipDenominations(gameState.balance);
        chips.forEach((chip, index) => {
            const value = chip.value * 1000; 
            const chipClass = getChipPreviewClass(value, denominations);
            const chipElement = $(`<div class="bet-chip-preview ${chipClass}"></div>`);
            if (index === 0) {
                chipStack.append(chipElement);
            } else {
                chipStack.prepend(chipElement);
            }
        });
        betCircle.append(chipStack);
    }
    function formatBetAmount(amount) {
        return amount.toString();
    }
    function calculateChipBreakdown(totalBet) {
        const currentDenominations = $('.chip-btn').map(function() {
            return parseInt($(this).data('value'));
        }).get().sort((a, b) => b - a);
        let chipValues = currentDenominations.length > 0 ? currentDenominations : generateChipDenominations(gameState.balance).sort((a, b) => b - a);
        const chips = [];
        let remaining = totalBet;
        for (let value of chipValues) {
            while (remaining >= value && chips.length < 5) {
                chips.push({ value: value / 1000 });
                remaining -= value;
            }
        }
        return chips;
    }
    function generateChipDenominations(balance) {
        const allChipValues = [10, 50, 100, 200, 500];
        return allChipValues;
    }
    function updateChipDenominations() {
        const balance = gameState.balance;
        const denominations = generateChipDenominations(balance);
        const existingChips = $('.chip-btn').map(function() {
            return parseInt($(this).data('value'));
        }).get();
        const needsRecreate = existingChips.length !== denominations.length ||
                             existingChips.some((val, index) => val !== denominations[index]);
        if (needsRecreate) {
            $('.chip-btn').remove();
            const chipTray = $('.chip-tray');
            denominations.forEach((value, idx) => {
                const formattedValue = formatChipValue(value);
                const chipClass = getChipClass(value, denominations);
                const chipButton = $(`
                    <button class="chip-btn" data-value="${value}" title="${formattedValue} chip">
                        <div class="chip ${chipClass}">${formattedValue}</div>
                    </button>
                `);
                chipTray.append(chipButton);
            });
            bindChipEvents();
        }
        updateChipButtonStates();
    }

    function updateChipButtonStates() {
        const balance = gameState.balance;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const gameInProgress = gameState.gameInProgress;
        const gamePhase = gameState.gamePhase;

        const canBet = !gameInProgress && (gamePhase === 'betting' || gamePhase === 'waiting');

        $('.chip-btn').each(function() {
            const chipValue = parseInt($(this).data('value'));
            const canAfford = balance >= chipValue;
            const isEnabled = canBet && canAfford;

            if (isEnabled) {
                $(this).removeClass('disabled').prop('disabled', false);
                $(this).attr('title', `${chipValue} chips`);
            } else {
                $(this).addClass('disabled').prop('disabled', true);
                if (!canBet) {
                    $(this).attr('title', `${chipValue} chips (Game in progress)`);
                } else if (!canAfford) {
                    $(this).attr('title', `${chipValue} chips (Insufficient Balance)`);
                }
            }
        });
    }
    function formatChipValue(value) {
        return value.toString();
    }
    function getChipClass(value, denominations = null) {
        if (!denominations) {
            denominations = generateChipDenominations(gameState.balance);
        }
        const chipClasses = ['chip-1', 'chip-2', 'chip-3', 'chip-4', 'chip-5'];
        const position = denominations.indexOf(value);
        return chipClasses[position % chipClasses.length];
    }
    function getChipPreviewClass(value, denominations = null) {
        if (!denominations) {
            denominations = generateChipDenominations(gameState.balance);
        }
        const chipPreviewClasses = ['chip-1-preview', 'chip-2-preview', 'chip-3-preview', 'chip-4-preview', 'chip-5-preview', 'chip-6-preview'];
        const position = denominations.indexOf(value);
        return chipPreviewClasses[position % chipPreviewClasses.length];
    }
    function bindChipEvents() {
        $(document).off('click.chipEvents').on('click.chipEvents', '.chip-btn', function() {
            if (gameState.gameInProgress || !gameState.gameStarted) return;
            if ($(this).hasClass('disabled') || $(this).prop('disabled')) return;

            const betValue = parseInt($(this).data('value'));
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];



            if (gameState.balance >= betValue && betValue > 0) {
                gameState.balance -= betValue;
                currentPlayer.bet += betValue;
                animateChipToBet(betValue, gameState.currentPlayerIndex);
                updateDisplay();
                safeSetTimeout(() => {
                    updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
                    if (currentPlayer.bet > 0 && !gameState.gameInProgress) {
                        showDealButton();
                        updateGameStatus(window.i18n ? window.i18n.t('messages.clickDoneToStart') : 'Click Done to start dealing cards');
                    }
                    updateChipButtonStates();
                    updateButtonStates();
                }, 400);
                $(this).addClass('selected');
                safeSetTimeout(() => $(this).removeClass('selected'), 300);
                playSound('chipPlace');
            } else {
                updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalance') : 'Insufficient balance!');
            }
        });
    }

    function showPlayerScore(playerIndex) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPosition}`).parent('.player-score').removeClass('hidden');
    }
    function hidePlayerScore(playerIndex) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPosition}`).parent('.player-score').addClass('hidden');
    }
    function hideAllPlayerScores() {
        gameState.players.forEach((player, index) => {
            hidePlayerScore(index);
        });
    }
    function displayCards() {
        $('#dealer-cards').empty();
        gameState.players.forEach((player, index) => {
            const htmlPosition = getPlayerHtmlPosition(index);
            $(`#player-cards-${htmlPosition}`).empty();
        });
        hideAllPlayerScores();
        gameState.dealerCards.forEach((card, index) => {
            safeSetTimeout(() => {
                if (index === 1 && gameState.gameInProgress && gameState.gamePhase !== 'results') {
                    $('#dealer-cards').append(createCardElement(card, true));
                } else {
                    $('#dealer-cards').append(createCardElement(card));
                }
                playSound('cardDeal');
            }, index * 100); 
        });
        gameState.players.forEach((player, playerIndex) => {
            player.cards.forEach((card, cardIndex) => {
                const dealDelay = (gameState.dealerCards.length + playerIndex * 2 + cardIndex) * 100;
                safeSetTimeout(() => {
                    const htmlPosition = getPlayerHtmlPosition(playerIndex);
                    $(`#player-cards-${htmlPosition}`).append(createCardElement(card));
                    playSound('cardDeal');
                }, dealDelay);
            });
            if (player.cards.length > 0) {
                const playerLastCardDelay = (gameState.dealerCards.length + playerIndex * 2 + player.cards.length - 1) * 100; 
                safeSetTimeout(() => {
                    showPlayerScore(playerIndex);
                }, playerLastCardDelay + 150); 
            }
        });
    }
    function addCardToPlayer(playerIndex, card) {
        gameState.players[playerIndex].cards.push(card);
        gameState.players[playerIndex].score = calculateScore(gameState.players[playerIndex].cards);
        safeSetTimeout(() => {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            $(`#player-cards-${htmlPosition}`).append(createCardElement(card));
            playSound('cardDeal');
            updateDisplay();
            safeSetTimeout(() => {
                showPlayerScore(playerIndex);
                check21PointEffect(playerIndex);
            }, 100);
        }, 200);
    }
    function startNewGame() {
        if (gameState.gameInProgress || gameState.gamePhase === 'dealing') {
            return;
        }

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (currentPlayer.bet === 0) {
            updateGameStatus(window.i18n ? window.i18n.t('messages.placeBetFirst') : 'Please place your bet first!');
            return;
        }

        gameState.lastBetAmount = currentPlayer.bet;

        gameState.gamePhase = 'dealing';
        updateSettingsButtonState();
        updateButtonStates();

        if (gameState.deck.length < 10) {
            triggerShuffleWithAnimation().then(() => {
                continueStartNewGame();
            });
            return;
        }
        continueStartNewGame();
    }
    function continueStartNewGame() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        currentPlayer.isActive = true;
        forceSyncSideBetDisplays();
        gameState.dealerCards = [];
        gameState.gameInProgress = true;
        gameState.dealerHiddenCard = null;
        gameState.gamePhase = 'dealing';
        gameState.currentTurnIndex = -1;
        gameState.players.forEach(player => {
            player.cards = [];
            player.score = 0;
            player.isBust = false;
        });
        hideGameStatus(); 
        hideDealButton();
        $('.chip-section, .betting-section').fadeOut();
        disableGameButtons();
        safeSetTimeout(() => {
            gameState.players.forEach((player, index) => {
                if (player.isActive) {
                    player.cards.push(dealCard());
                }
            });
            gameState.dealerCards.push(dealCard());
            gameState.players.forEach((player, index) => {
                if (player.isActive) {
                    player.cards.push(dealCard());
                }
            });
            gameState.dealerCards.push(dealCard());
            gameState.dealerHiddenCard = gameState.dealerCards[1];
            gameState.players.forEach(player => {
                if (player.isActive) {
                    player.score = calculateScore(player.cards);
                }
            });
            gameState.dealerScore = calculateScore([gameState.dealerCards[0]]);
            displayCards();
            updateDisplay();
            safeSetTimeout(() => {
                gameState.players.forEach((player, index) => {
                    if (player.isActive) {
                        showPlayerScore(index);
                        check21PointEffect(index);
                    }
                });
            }, 500);
            safeSetTimeout(() => {
                startPlayerTurns();
            }, 500);
        }, 200); 
    }


    function checkDealerHoleCard() {
        if (gameState.dealerCards.length >= 2) {
            const holeCard = gameState.dealerCards[1]; 
            const holeCardValue = getCardValue(holeCard.value);

            if (holeCardValue === 10) {
                gameState.dealerHasBlackjack = true;
                showGameStatus('Checking dealer cards...');

                setTimeout(() => {
                    revealDealerHiddenCard();
                    setTimeout(() => {
                        handleDealerBlackjack();
                    }, 500);
                }, 1000);
            } else {
                startPlayerTurns();
            }
        } else {
            startPlayerTurns();
        }
    }



    function handleDealerBlackjack() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        let totalWinnings = 0;
        let resultMessage = '';

        resultMessage = 'Dealer Pontoon!';

        if (isPontoon(currentPlayer.cards)) {
            totalWinnings += currentPlayer.bet;
            resultMessage = 'Both have Pontoon! Push - bet returned.';
        } else {
            resultMessage += ` You lost $${currentPlayer.bet}.`;
        }

        gameState.balance += totalWinnings;
        updateDisplay();

        showGameStatus(resultMessage + ' Click anywhere to continue...');
        $('.bottom-controls').addClass('show');

        setTimeout(() => {
            const clickHandler = function() {
                $(document).off('click', clickHandler);
                $('.game-table').off('click', clickHandler);
                resetForNextGame();
            };

            $(document).on('click', clickHandler);
            $('.game-table').on('click', clickHandler);
        }, 300);
    }

    function startPlayerTurns() {
        gameState.gamePhase = 'playing';
        updateSettingsButtonState();
        updateButtonStates();
        gameState.currentTurnIndex = 0;
        nextPlayerTurn();
    }
    function nextPlayerTurn() {
        while (gameState.currentTurnIndex < gameState.players.length) {
            const player = gameState.players[gameState.currentTurnIndex];
            if (player.isActive && !player.isBust) {
                if (player.score === 21) {
                    safeSetTimeout(() => {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }, 500);
                    return;
                } else {
                    setActiveTurn(gameState.currentTurnIndex);
                    showGameStatus(`Your turn - Twist or Stick?`);
                    enableGameButtons();
                    updateButtonStates();
                    return;
                }
            }
            gameState.currentTurnIndex++;
        }
        clearActiveTurn();
        dealerTurn();
    }



    function getBasicStrategyHint(playerCards, playerScore, dealerUpCard, canDouble, canSplit) {
        const dealerValue = getCardValue(dealerUpCard.value);

        if (canSplit && playerCards.length === 2 && playerCards[0].value === playerCards[1].value) {
            const cardValue = playerCards[0].value;
            if (cardValue === 'A' || cardValue === '8') {
                return 'split';
            }
            if (cardValue === '9' && dealerValue !== 7 && dealerValue !== 10 && dealerValue !== 11) {
                return 'split';
            }
            if ((cardValue === '2' || cardValue === '3' || cardValue === '7') && dealerValue <= 7) {
                return 'split';
            }
            if (cardValue === '6' && dealerValue <= 6) {
                return 'split';
            }
        }

        const hasAce = playerCards.some(card => card.value === 'A');
        if (hasAce && playerScore <= 21) {
            const aceCount = playerCards.filter(card => card.value === 'A').length;
            const otherCardsSum = playerCards.filter(card => card.value !== 'A')
                .reduce((sum, card) => sum + getCardValue(card.value), 0);

            if (aceCount === 1 && otherCardsSum <= 10) {
                        if (playerScore >= 19) return 'stick';
        if (playerScore === 18) {
            if (dealerValue <= 6) return canDouble ? 'buy' : 'stick';
            if (dealerValue === 7 || dealerValue === 8) return 'stick';
            return 'twist';
        }
        if (playerScore === 17) {
            if (dealerValue <= 6) return canDouble ? 'buy' : 'twist';
            return 'twist';
        }
        if (playerScore >= 15 && playerScore <= 16) {
            if (dealerValue <= 6) return canDouble ? 'buy' : 'twist';
            return 'twist';
        }
        if (playerScore >= 13 && playerScore <= 14) {
            if (dealerValue <= 6) return canDouble ? 'buy' : 'twist';
            return 'twist';
        }
        return 'twist';
            }
        }

            if (playerScore >= 17) return 'stick';
    if (playerScore >= 13 && playerScore <= 16) {
        return dealerValue <= 6 ? 'stick' : 'twist';
    }
    if (playerScore === 12) {
        return (dealerValue >= 4 && dealerValue <= 6) ? 'stick' : 'twist';
    }
    if (playerScore === 11) {
        return canDouble ? 'buy' : 'twist';
    }
    if (playerScore === 10) {
        return (dealerValue <= 9 && canDouble) ? 'buy' : 'twist';
    }
    if (playerScore === 9) {
        return (dealerValue >= 3 && dealerValue <= 6 && canDouble) ? 'buy' : 'twist';
    }

    return 'twist';
    }

    function showHint() {
        if (!gameState.gameInProgress || gameState.currentTurnIndex !== gameState.currentPlayerIndex) return;

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const dealerUpCard = gameState.dealerCards[0];

        const canDouble = currentPlayer.cards.length === 2 && currentPlayer.bet <= gameState.balance;
        const canSplit = canPlayerSplit(gameState.currentPlayerIndex);

        const hint = getBasicStrategyHint(currentPlayer.cards, currentPlayer.score, dealerUpCard, canDouble, canSplit);

        $('.action-btn').removeClass('hint-highlight');

        let hintMessage = '';
        switch(hint) {
            case 'twist':
                $('#twist').addClass('hint-highlight');
                hintMessage = 'Basic strategy suggests: Twist';
                break;
            case 'stick':
                $('#stick').addClass('hint-highlight');
                hintMessage = 'Basic strategy suggests: Stick';
                break;
            case 'double':
                if (canDouble) {
                    $('#double-down').addClass('hint-highlight');
                    hintMessage = 'Basic strategy suggests: Double Down';
                } else {
                    $('#twist').addClass('hint-highlight');
                    hintMessage = 'Basic strategy suggests: Twist (Double not available)';
                }
                break;
            case 'split':
                if (canSplit) {
                    $('#split').addClass('hint-highlight');
                    hintMessage = 'Basic strategy suggests: Split';
                } else {
                    $('#twist').addClass('hint-highlight');
                    hintMessage = 'Basic strategy suggests: Twist (Split not available)';
                }
                break;
            default:
                $('#twist').addClass('hint-highlight');
                hintMessage = 'Basic strategy suggests: Twist';
        }

        updateGameStatus(hintMessage);

        setTimeout(() => {
            $('.action-btn').removeClass('hint-highlight');
            updateGameStatus('Your turn - Twist or Stick?');
        }, 3000);
    }

    function getCardValue(cardValue) {
        switch(cardValue) {
            case 'A': return 11; 
            case 'K':
            case 'Q':
            case 'J': return 10;
            default: return parseInt(cardValue);
        }
    }
    function playerTwist() {
        if (!gameState.gameInProgress || gameState.currentTurnIndex !== gameState.currentPlayerIndex) return;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        disableGameButtons();
        setTimeout(() => {
            if (currentPlayer.splitHands.length > 0) {
                const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
                const newCard = dealCard();
                currentHand.cards.push(newCard);
                currentHand.score = calculateScore(currentHand.cards);
                currentHand.hasDrawnCard = true; 
                playSound('deal');
                const newCardIndex = currentHand.cards.length - 1;
                displaySplitHands(gameState.currentPlayerIndex, newCardIndex);
                updateDisplay();
                
                if (isFiveCardTrick(currentHand.cards)) {
                    showFiveCardTrickEffect(gameState.currentPlayerIndex);
                    setTimeout(() => {
                        moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                            if (!hasMoreHands) {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            } else {
                                showGameStatus(`Hand ${currentPlayer.currentHandIndex + 1} of ${currentPlayer.splitHands.length} - Twist or Stick?`);
                                $('.bottom-controls').addClass('show');
                                enableGameButtons();
                                updateButtonStates();
                            }
                        });
                    }, 500);
                } else if (currentHand.score > 21) {
                    showBustEffect(gameState.currentPlayerIndex);
                    setTimeout(() => {
                        moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                            if (!hasMoreHands) {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            } else {
                                showGameStatus(`Hand ${currentPlayer.currentHandIndex + 1} of ${currentPlayer.splitHands.length} - Twist or Stick?`);
                                $('.bottom-controls').addClass('show');
                                enableGameButtons();
                                updateButtonStates();
                            }
                        });
                    }, 500);
                } else if (currentHand.score === 21) {
                    show21PointEffect(gameState.currentPlayerIndex);
                    setTimeout(() => {
                        moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                            if (!hasMoreHands) {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            } else {
                                showGameStatus(`Hand ${currentPlayer.currentHandIndex + 1} of ${currentPlayer.splitHands.length} - Twist or Stick?`);
                                $('.bottom-controls').addClass('show');
                                enableGameButtons();
                                updateButtonStates();
                            }
                        });
                    }, 500); 
                } else {
                    showGameStatus(`Hand ${currentPlayer.currentHandIndex + 1} of ${currentPlayer.splitHands.length} - Continue Twist or Stick?`);
                    $('.bottom-controls').addClass('show'); 
                    enableGameButtons();
                    updateButtonStates();
                }
            } else {
                addCardToPlayer(gameState.currentPlayerIndex, dealCard());
                
                if (isFiveCardTrick(currentPlayer.cards)) {
                    showFiveCardTrickEffect(gameState.currentPlayerIndex);
                    setTimeout(() => {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }, 500);
                } else if (currentPlayer.score > 21) {
                    setTimeout(() => {
                        showBustEffect(gameState.currentPlayerIndex);
                        setTimeout(() => {
                            gameState.currentTurnIndex++;
                            nextPlayerTurn();
                        }, 500); 
                    }, 500);
                } else if (currentPlayer.score === 21) {
                    const playerPosition = $(`.player-position[data-position="${gameState.currentPlayerIndex}"]`);
                    if (isPontoon(currentPlayer.cards)) {
                        playerPosition.addClass('pontoon');
                    } else {
                        playerPosition.addClass('twenty-one');
                    }
                    setTimeout(() => {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }, 500);
                } else {
                    showGameStatus('Continue Twist or Stick?');
                    $('.bottom-controls').addClass('show'); 
                    enableGameButtons();
                    updateButtonStates();
                }
            }
        }, 300);
    }
    function playerStick() {
        if (!gameState.gameInProgress || gameState.currentTurnIndex !== gameState.currentPlayerIndex) return;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        
        if (currentPlayer.splitHands.length > 0) {
            const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
            if (currentHand.score < 15) {
                updateGameStatus('You must reach 15 or more points to stick!');
                return;
            }
            if (!currentHand.hasDrawnCard) {
                updateGameStatus('You must draw at least one card after splitting before you can stick!');
                return;
            }
        } else {
            if (currentPlayer.score < 15) {
                updateGameStatus('You must reach 15 or more points to stick!');
                return;
            }
        }
        
        disableGameButtons();
        setTimeout(() => {
            if (currentPlayer.splitHands.length > 0) {
                moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                    if (!hasMoreHands) {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    } else {
                        showGameStatus(`Hand ${currentPlayer.currentHandIndex + 1} of ${currentPlayer.splitHands.length} - Twist or Stick?`);
                        $('.bottom-controls').addClass('show');
                        enableGameButtons();
                        updateButtonStates();
                    }
                });
            } else {
                gameState.currentTurnIndex++;
                nextPlayerTurn();
            }
        }, 500);
    }
    function revealDealerHiddenCard() {
        if (gameState.dealerCards.length >= 2) {
            const hiddenCardElement = $('#dealer-cards .card-back, #dealer-cards .card.hidden');
            if (hiddenCardElement.length > 0) {
                const revealedCard = createCardElement(gameState.dealerCards[1]);
                hiddenCardElement.replaceWith(revealedCard);
                playSound('cardFlip');
            }
        }
        gameState.dealerHiddenCard = null;
        gameState.dealerScore = calculateScore(gameState.dealerCards);
        updateDisplay();
    }

    function dealerTurn() {
        gameState.gamePhase = 'dealer';
        updateSettingsButtonState();
        hideGameStatus(); 
        $('.bottom-controls').removeClass('show');
        disableGameButtons();

        setTimeout(() => {
            revealDealerHiddenCard();
            dealerHitSequence();
        }, 200);
    }
    function dealerHitSequence() {
        const shouldDealerHit = shouldDealerHitStandard();
        if (shouldDealerHit) {
            setTimeout(() => {
                gameState.dealerCards.push(dealCard());
                gameState.dealerScore = calculateScore(gameState.dealerCards);
                $('#dealer-cards').append(createCardElement(gameState.dealerCards[gameState.dealerCards.length - 1]));
                updateDisplay();
                playSound('cardDeal');
                if (gameState.dealerScore === 21) {
                    if (isPontoon(gameState.dealerCards)) {
                        showDealerEffect('pontoon');
                    } else {
                        showDealerEffect('twenty-one');
                    }
                }
                if (gameState.dealerScore > 21) {
                    showDealerEffect('bust');
                }
                dealerHitSequence();
            }, 300);
        } else {
            if (gameState.dealerScore > 21) {
                showDealerEffect('bust');
            }
            setTimeout(() => {
                showGameResults();
            }, 500);
        }
    }
    function shouldDealerHitStandard() {
        if (gameState.dealerScore <= 16) {
            return true;
        }
        if (gameState.dealerScore >= 18) {
            return false;
        }
        if (gameState.dealerScore === 17) {
            return isSoft17(gameState.dealerCards);
        }
        return false;
    }
    function isSoft17(cards) {
        if (!cards || cards.length === 0) return false;
        let aces = 0;
        let otherCardsTotal = 0;
        for (let card of cards) {
            if (card.value === 'A') {
                aces++;
            } else if (['J', 'Q', 'K'].includes(card.value)) {
                otherCardsTotal += 10;
            } else {
                otherCardsTotal += parseInt(card.value);
            }
        }
        return aces >= 1 && (otherCardsTotal + (aces - 1)) === 6;
    }
    function doubleDown() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (!gameState.gameInProgress ||
            gameState.currentTurnIndex !== gameState.currentPlayerIndex) {
            return;
        }

        let currentCards = [];
        if (currentPlayer.splitHands.length > 0) {
            currentCards = currentPlayer.splitHands[currentPlayer.currentHandIndex].cards;
        } else {
            currentCards = currentPlayer.cards;
        }

        if (currentCards.length < 2 || currentCards.length > 4) {
            updateGameStatus('Buy is only available with 2-4 cards!');
            return;
        }

        if (currentPlayer.splitHands.length > 0) {
            const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
            if (currentHand.hasDoubled) {
                updateGameStatus('You can only buy once per hand!');
                return;
            }
            if (currentHand.bet > gameState.balance) {
                updateGameStatus('Insufficient balance for buy!');
                return;
            }
        } else {
            if (currentPlayer.hasDoubled) {
                updateGameStatus('You can only buy once per hand!');
                return;
            }
            if (currentPlayer.bet > gameState.balance) {
                updateGameStatus('Insufficient balance for buy!');
                return;
            }
        }
        disableGameButtons();
        
        if (currentPlayer.splitHands.length > 0) {
            const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
            gameState.balance -= currentHand.bet;
            currentHand.bet *= 2;
            currentHand.hasDoubled = true;
            
            setTimeout(() => {
                const newCard = dealCard();
                currentHand.cards.push(newCard);
                currentHand.score = calculateScore(currentHand.cards);
                currentHand.hasDrawnCard = true;
                playSound('deal');
                const newCardIndex = currentHand.cards.length - 1;
                displaySplitHands(gameState.currentPlayerIndex, newCardIndex);
                updateDisplay();
                
                const totalBet = currentPlayer.splitHands.reduce((sum, hand) => sum + hand.bet, 0);
                updateBetDisplay(gameState.currentPlayerIndex, totalBet);
                
                setTimeout(() => {
                    if (currentHand.score > 21) {
                        showBustEffect(gameState.currentPlayerIndex);
                    }
                    setTimeout(() => {
                        moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                            if (!hasMoreHands) {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            } else {
                                showGameStatus(`Hand ${currentPlayer.currentHandIndex + 1} of ${currentPlayer.splitHands.length} - Twist or Stick?`);
                                $('.bottom-controls').addClass('show');
                                enableGameButtons();
                                updateButtonStates();
                            }
                        });
                    }, 300);
                }, 300);
            }, 300);
        } else {
            gameState.balance -= currentPlayer.bet;
            currentPlayer.bet *= 2;
            currentPlayer.hasDoubled = true;
            
            setTimeout(() => {
                addCardToPlayer(gameState.currentPlayerIndex, dealCard());
                setTimeout(() => {
                    if (currentPlayer.score > 21) {
                        showBustEffect(gameState.currentPlayerIndex);
                        setTimeout(() => {
                            gameState.currentTurnIndex++;
                            nextPlayerTurn();
                        }, 300);
                    } else {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }
                }, 300); 
            }, 300);
        }
        
        updateDisplay();
    }
    function showGameResults() {
        gameState.gamePhase = 'results';
        updateGameStatus('Calculating results...');
        let totalWinnings = 0;
        gameState.winSoundPlayed = false;
        
        const dealerRank = getHandRank(gameState.dealerCards);
        console.log('Dealer hand analysis:', {
            cards: gameState.dealerCards.map(c => `${c.value}${c.suit}`),
            rank: dealerRank
        });
        
        gameState.players.forEach((player, index) => {
            if (player.isActive) {
                let totalPlayerWinnings = 0;
                let resultClass = '';
                if (player.splitHands.length > 0) {
                    let handResults = [];
                    player.splitHands.forEach((hand, handIndex) => {
                        let handWinnings = 0;
                        let handResult = '';
                        const playerRank = getHandRank(hand.cards);
                        
                        if (playerRank.rank === 0) { 
                            handResult = 'lost';
                            handWinnings = 0;
                        } else if (dealerRank.rank === 0) {
                            handResult = 'won';
                            if (playerRank.rank === 4) { 
                                handResult = 'pontoon';
                                handWinnings = hand.bet * 3; 
                            } else if (playerRank.rank === 3) { 
                                handResult = 'five-card-trick';
                                handWinnings = hand.bet * 3; 
                            } else {
                                handWinnings = hand.bet * 2;
                            }
                        } else {
                            console.log(`Split hand ${handIndex + 1} comparison:`, {
                                playerCards: hand.cards.map(c => `${c.value}${c.suit}`),
                                playerRank: playerRank,
                                dealerCards: gameState.dealerCards.map(c => `${c.value}${c.suit}`),
                                dealerRank: dealerRank,
                                comparison: `Player ${playerRank.score} vs Dealer ${dealerRank.score}`,
                                rankComparison: `Player rank ${playerRank.rank} vs Dealer rank ${dealerRank.rank}`
                            });

                            if (playerRank.rank > dealerRank.rank) {
                                handResult = 'won';
                                if (playerRank.rank === 4) { // Pontoon
                                    handResult = 'pontoon';
                                    handWinnings = hand.bet * 3; // 2:1 payout
                                } else if (playerRank.rank === 3) { // Five Card Trick
                                    handResult = 'five-card-trick';
                                    handWinnings = hand.bet * 3; // 2:1 payout
                                } else {
                                    handWinnings = hand.bet * 2; // 1:1 payout
                                }
                            } else if (playerRank.rank < dealerRank.rank) {
                                handResult = 'lost';
                                handWinnings = 0;
                            } else {
                                console.log(`Split hand ${handIndex + 1} score comparison:`, {
                                    playerScore: playerRank.score,
                                    dealerScore: dealerRank.score,
                                    result: playerRank.score > dealerRank.score ? 'Player wins' :
                                           playerRank.score < dealerRank.score ? 'Dealer wins' : 'Tie (Dealer wins)'
                                });

                                if (playerRank.score > dealerRank.score) {
                                    handResult = 'won';
                                    handWinnings = hand.bet * 2; // 1:1 payout
                                } else if (playerRank.score < dealerRank.score) {
                                    handResult = 'lost';
                                    handWinnings = 0;
                                } else {
                                    handResult = 'lost';
                                    handWinnings = 0;
                                }
                            }
                        }
                        handResults.push(handResult);
                        totalPlayerWinnings += handWinnings;
                    });
                    if (handResults.every(result => result === 'lost' || result === 'bust')) {
                        resultClass = 'lost';
                    } else if (handResults.some(result => result === 'won' || result === 'pontoon' || result === 'five-card-trick')) {
                        resultClass = 'won';
                    } else if (handResults.every(result => result === 'lost')) {
                        resultClass = 'lost';
                    } else {
                        resultClass = 'lost'; 
                    }
                    setTimeout(() => {
                        applySplitHandsSettlement(index, handResults);
                    }, 300);
                } else {
                    const playerRank = getHandRank(player.cards);
                    
                    if (playerRank.rank === 0) { 
                        resultClass = 'bust lost';
                        totalPlayerWinnings = 0;
                    } else if (dealerRank.rank === 0) { // Dealer bust
                        resultClass = 'won';
                        if (playerRank.rank === 4) { // Pontoon
                            resultClass = 'pontoon won';
                            totalPlayerWinnings = player.bet * 3; // 2:1 payout
                        } else if (playerRank.rank === 3) { // Five Card Trick
                            resultClass = 'five-card-trick won';
                            totalPlayerWinnings = player.bet * 3; // 2:1 payout
                        } else {
                            totalPlayerWinnings = player.bet * 2; // 1:1 payout
                        }
                    } else {
                        // Compare hand ranks
                        if (playerRank.rank > dealerRank.rank) {
                            resultClass = 'won';
                            if (playerRank.rank === 4) { // Pontoon
                                resultClass = 'pontoon won';
                                totalPlayerWinnings = player.bet * 3; // 2:1 payout
                            } else if (playerRank.rank === 3) { // Five Card Trick
                                resultClass = 'five-card-trick won';
                                totalPlayerWinnings = player.bet * 3; // 2:1 payout
                            } else {
                                totalPlayerWinnings = player.bet * 2; // 1:1 payout
                            }
                        } else if (playerRank.rank < dealerRank.rank) {
                            resultClass = 'lost';
                            totalPlayerWinnings = 0;
                        } else {
                            // Same rank, compare scores
                            console.log('Main hand comparison:', {
                                playerCards: player.cards.map(c => `${c.value}${c.suit}`),
                                playerRank: playerRank,
                                dealerCards: gameState.dealerCards.map(c => `${c.value}${c.suit}`),
                                dealerRank: dealerRank,
                                comparison: `Player ${playerRank.score} vs Dealer ${dealerRank.score}`,
                                rankComparison: `Player rank ${playerRank.rank} vs Dealer rank ${dealerRank.rank}`,
                                result: playerRank.score > dealerRank.score ? 'Player wins' :
                                       playerRank.score < dealerRank.score ? 'Dealer wins' : 'Tie (Dealer wins)'
                            });

                            if (playerRank.score > dealerRank.score) {
                                resultClass = 'won';
                                totalPlayerWinnings = player.bet * 2; // 1:1 payout
                            } else if (playerRank.score < dealerRank.score) {
                                resultClass = 'lost';
                                totalPlayerWinnings = 0;
                            } else {
                                // New British 21 rule: dealer wins on ties
                                resultClass = 'lost';
                                totalPlayerWinnings = 0;
                            }
                        }
                    }
                    
                    const htmlPosition = getPlayerHtmlPosition(index);
                    const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
                    playerPosition.removeClass('bust lost won push blackjack pontoon five-card-trick');
                    setTimeout(() => {
                        playerPosition.addClass(resultClass);
                    }, 200); 
                }
                if (index === gameState.currentPlayerIndex) {
                    totalWinnings = totalPlayerWinnings;
                }
            }
        });
        
        if (totalWinnings > 0) {
            gameState.balance += totalWinnings;
            updateDisplay();
        }

        cacheUserBalance();
        setTimeout(() => {
            let dealerWon = false;
            let dealerLost = false;
            gameState.players.forEach((player, index) => {
                if (player.isActive) {
                    const playerRank = getHandRank(player.cards);
                    if (playerRank.rank > 0 && (dealerRank.rank === 0 || playerRank.rank > dealerRank.rank || 
                        (playerRank.rank === dealerRank.rank && playerRank.score > dealerRank.score))) {
                        dealerLost = true;
                    } else if (playerRank.rank === 0 || (dealerRank.rank > 0 && 
                        (dealerRank.rank > playerRank.rank || (playerRank.rank === dealerRank.rank && dealerRank.score > playerRank.score)))) {
                        dealerWon = true;
                    }
                }
            });
            if (!$('.dealer-section').hasClass('pontoon twenty-one bust')) {
                if (dealerLost && !dealerWon) {
                    showDealerEffect('lost');
                } else if (dealerWon && !dealerLost) {
                    showDealerEffect('won');
                }
            }
        }, 200);

        safeSetTimeout(() => {
            showGameResultsAndWaitForClick(totalWinnings);
        }, 300);
    }

    function showGameResultsAndWaitForClick(totalWinnings) {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        let resultMessage = '';
        let netResult = 0;

        if (totalWinnings > currentPlayer.bet) {
            netResult = totalWinnings - currentPlayer.bet;
            resultMessage = `You won $${netResult}!`;
        } else if (totalWinnings === currentPlayer.bet) {
            resultMessage = 'It\'s a push!';
        } else {
            netResult = currentPlayer.bet;
            resultMessage = `You lost $${netResult}!`;
        }

        showGameStatus(resultMessage + ' Click anywhere to continue...');
        $('.bottom-controls').addClass('show');

        setTimeout(() => {
            const clickHandler = function() {
                $(document).off('click', clickHandler);
                $('.game-table').off('click', clickHandler);
                resetForNextGame();
            };

            $(document).on('click', clickHandler);
            $('.game-table').on('click', clickHandler);
        }, 300); 
    }
    function resetForNextGame() {
        cleanupAnimationElements();

        checkCardIntegrity('Before round reset');

        let cardsDiscarded = 0;

        gameState.dealerCards.forEach(card => {
            if (card) {
                gameState.discardPile.push(card);
                cardsDiscarded++;
            }
        });

        gameState.players.forEach(player => {
            player.cards.forEach(card => {
                if (card) {
                    gameState.discardPile.push(card);
                    cardsDiscarded++;
                }
            });

            if (player.splitHands && player.splitHands.length > 0) {
                player.splitHands.forEach(hand => {
                    hand.cards.forEach(card => {
                        if (card) {
                            gameState.discardPile.push(card);
                            cardsDiscarded++;
                        }
                    });
                });
            }
        });

        if (cardsDiscarded > 0) {
            console.log(`Round ended: ${cardsDiscarded} cards moved to discard pile`);
        }

        gameState.dealerCards = [];
        gameState.dealerScore = 0;
        gameState.dealerHiddenCard = null;

        gameState.players.forEach(player => {
            player.cards = [];
            player.score = 0;
            player.splitHands = [];
            player.currentHandIndex = 0;
        });

        checkCardIntegrity('After round reset');

        if (gameSettings.pendingDeckCount && gameSettings.pendingDeckCount !== gameSettings.deckCount) {
            gameSettings.deckCount = gameSettings.pendingDeckCount;
            gameSettings.pendingDeckCount = null;
            saveDeckSettings();

            createDeck();
            shuffleDeck();

            updateGameStatus(`Deck setting applied: ${gameSettings.deckCount} deck${gameSettings.deckCount > 1 ? 's' : ''}`);
        }

        gameState.gameInProgress = false;
        gameState.gamePhase = 'betting';
        updateSettingsButtonState();
        updateButtonStates();
        gameState.currentTurnIndex = -1;
        gameState.playerReady = false;
        gameState.autoStartScheduled = false;
        gameState.dealerHasBlackjack = false;
        $('.dealer-section').removeClass('bust lost won blackjack pontoon twenty-one');
        gameState.players.forEach((player, index) => {
            player.bet = 0;
            player.isActive = false;
            player.cards = [];
            player.score = 0;
            player.isBust = false;
            player.splitHands = [];
            player.currentHandIndex = 0;
            player.canSplit = false;
            player.hasDoubled = false;
            player.sideBets = {
                perfectPairs: 0,
                twentyOnePlusThree: 0
            };
            const htmlPosition = getPlayerHtmlPosition(index);
            $(`.player-position[data-position="${htmlPosition}"]`).removeClass('bust lost won push blackjack pontoon twenty-one five-card-trick active-turn');
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-chips-stack`).remove();
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-spot`).removeClass('has-bet');
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-amount`).text('0');
        });
        updateDeckDisplay();
        $('#dealer-cards').empty();
        $('.player-cards-area').empty();
        hideAllPlayerScores();

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (gameState.lastBetAmount > 0 && gameState.balance >= gameState.lastBetAmount) {
            gameState.balance -= gameState.lastBetAmount;
            currentPlayer.bet = gameState.lastBetAmount;

            updateChipDenominations();

            setTimeout(() => {
                animateChipToBet(gameState.lastBetAmount, gameState.currentPlayerIndex);
                updateDisplay();
                setTimeout(() => {
                    updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
                    showDealButton(); 
                    updateGameStatus(window.i18n ? window.i18n.t('messages.clickDoneToStart') : 'Click Done to start dealing cards');
                }, 400);
            }, 100);
        } else {
            showGameStatus('Place your bets for next round');
            hideDealButton();
        }

        $('.game-actions-section').fadeOut();
        updateDisplay();
        updateChipDenominations();
        updateChipButtonStates();
        $('.chip-section, .betting-section').fadeIn();
        forceSyncSideBetDisplays();
        gameState.gameStarted = true;
        if (gameState.balance === 0) {
            setTimeout(() => {
                checkGameOverCondition();
            }, 1000);
        } else {
            startBettingCountdown();
        }
    }

    function updateGameStatus(message) {
        if (domCache.$gameStatus) {
            domCache.$gameStatus.text(message);
        } else {
            $('#game-status').text(message);
        }
    }

    function showGameStatus(message) {
        updateGameStatus(message);
        $('#game-status').removeClass('hidden').addClass('show');
    }

    function hideGameStatus() {
        $('#game-status').removeClass('show').addClass('hidden');
    }
    function startGame() {
        if (gameState.gameStarted) return;

        if (!gameState.hasAutoFullscreened) {
            gameState.hasAutoFullscreened = true;
            autoFullscreen();
        }

        initializePlayers();
        updatePlayerPositionsDisplay();
        gameState.gameStarted = true;
        gameState.gamePhase = 'betting';
        updateSettingsButtonState();
        updateButtonStates();

        $('#game-status').show();
        $('.dealer-section').addClass('show').show();
        $('.players-area').addClass('show').show();
        $('.bottom-controls').addClass('show').show();
        $('.top-status').show();
        $('.deck-area').show();

        updateChipDenominations();
        updateChipButtonStates();
        updateButtonStates();
        hideDealButton();
        showGameStatus('Welcome! Please place your bet to start playing (Min: 1).');
        startBettingCountdown();
    }
    function startBettingCountdown() {
        updateDealButtonState();
    }

    function showBustEffect(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.splitHands && player.splitHands.length > 0) {
            const currentHand = player.splitHands[player.currentHandIndex];
            currentHand.isBust = true;
            currentHand.effectClass = 'bust';
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const cardsContainer = $(`#player-cards-${htmlPosition}`);
            const currentHandContainer = cardsContainer.find('.current-hand');
            currentHandContainer.addClass('bust');
        } else {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            playerPosition.addClass('bust');
            player.isBust = true;
        }
    }
    function show21PointEffect(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.splitHands && player.splitHands.length > 0) {
            const currentHand = player.splitHands[player.currentHandIndex];
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const cardsContainer = $(`#player-cards-${htmlPosition}`);
            const currentHandContainer = cardsContainer.find('.current-hand');
            currentHandContainer.addClass('twenty-one');
            currentHand.effectClass = 'twenty-one';
        } else {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
                                if (isPontoon(player.cards)) {
                        playerPosition.addClass('pontoon');
                    } else {
                        playerPosition.addClass('twenty-one');
                    }
        }
    }
    function showDealerEffect(effectType) {
        const dealerSection = $('.dealer-section');
        dealerSection.removeClass('bust lost won blackjack twenty-one');
        dealerSection.addClass(effectType);
    }
    function check21PointEffect(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.score === 21) {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            if (isPontoon(player.cards)) {
                playerPosition.addClass('pontoon');
            } else {
                playerPosition.addClass('twenty-one');
            }
        }
    }
    function createFlyingChip(chipClass, startPos, endPos, delay = 0) {
        const chip = $(`<div class="flying-chip ${chipClass}"></div>`);
        chip.css({
            left: startPos.left + 'px',
            top: startPos.top + 'px'
        });
        $('body').append(chip);
        safeSetTimeout(() => {
            chip.css({
                transition: 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                left: endPos.left + 'px',
                top: endPos.top + 'px',
                opacity: 0
            });
            safeSetTimeout(() => {
                chip.css({
                    transform: 'scale(1.2) rotate(180deg)'
                });
            }, 150);
            safeSetTimeout(() => {
                chip.css({
                    transform: 'scale(0.8) rotate(360deg)'
                });
            }, 300);
            safeSetTimeout(() => {
                chip.remove();
            }, 600);
        }, delay);
    }
    function animateChipToBet(chipValue, playerIndex) {
        const chipButton = $(`.chip-btn[data-value="${chipValue}"]`);
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        if (chipButton.length && betCircle.length) {
            const startPos = chipButton.offset();
            const endPos = betCircle.offset();
            const denominations = generateChipDenominations(gameState.balance);
            const chipClass = getChipPreviewClass(chipValue, denominations);
            createFlyingChip(chipClass, startPos, endPos);
        }
    }
    function animateSplitBetChips(totalBet, playerIndex) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        const playerAvatar = $(`.player-position[data-position="${htmlPosition}"] .player-avatar`);
        if (!betCircle.length || !playerAvatar.length) return;
        const chips = calculateChipBreakdown(totalBet);
        const endPos = betCircle.offset();
        const startPos = playerAvatar.offset();
        chips.forEach((chip, index) => {
            setTimeout(() => {
                const chipValue = chip.value * 1000; 
                const denominations = generateChipDenominations(gameState.balance);
                const chipClass = getChipPreviewClass(chipValue, denominations);
                const offsetEndPos = {
                    left: endPos.left + (index * 2),
                    top: endPos.top - (index * 2)
                };
                const offsetStartPos = {
                    left: startPos.left + (index * 3),
                    top: startPos.top + (index * 2)
                };
                createFlyingChip(chipClass, offsetStartPos, offsetEndPos);
            }, index * 100); 
        });
    }
    function animateChipToSideBet(chipValue, playerIndex, betType) {
        const chipButton = $(`.side-bet-btn[data-type="${betType}"][data-value="${chipValue}"]`);
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const sideBetSpot = $(`.player-position[data-position="${htmlPosition}"] .side-bet-spot[data-type="${betType}"]`);
        if (chipButton.length && sideBetSpot.length) {
            const startPos = chipButton.offset();
            const endPos = sideBetSpot.offset();
            const denominations = generateChipDenominations(gameState.balance);
            const chipClass = getChipPreviewClass(chipValue, denominations);
            createFlyingChip(chipClass, startPos, endPos);
        }
    }

    function setActiveTurn(playerIndex) {
        $('.player-position').removeClass('active-turn');
        if (playerIndex >= 0) {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            $(`.player-position[data-position="${htmlPosition}"]`).addClass('active-turn');
        }
    }
    function clearActiveTurn() {
        $('.player-position').removeClass('active-turn');
    }
    function showActionControls() {
        $('.chip-section, .betting-section').fadeOut();
        $('.game-actions-section').fadeIn();
    }
    function showDealButton() {
        $('#deal-cards').prop('disabled', false);
    }
    function hideDealButton() {
        $('#deal-cards').prop('disabled', true);
    }
    function enableGameButtons() {
        showActionControls();
        $('#twist, #stick').prop('disabled', false);
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        
        // Pontoon rule: Can Buy (double down) with 2, 3, 4 cards, but only once per hand
        let canDoubleDown = true;
        let requiredBalance = 0;
        let currentCards = [];

        if (currentPlayer.splitHands.length > 0) {
            const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
            requiredBalance = currentHand.bet;
            currentCards = currentHand.cards;
            canDoubleDown = !currentHand.hasDoubled &&
                           gameState.balance >= requiredBalance &&
                           currentCards.length >= 2 &&
                           currentCards.length <= 4;
        } else {
            requiredBalance = currentPlayer.bet;
            currentCards = currentPlayer.cards;
            canDoubleDown = !currentPlayer.hasDoubled &&
                           gameState.balance >= requiredBalance &&
                           currentCards.length >= 2 &&
                           currentCards.length <= 4;
        }
        
        $('#double-down').prop('disabled', !canDoubleDown);
        $('#split').prop('disabled', !canPlayerSplit(gameState.currentPlayerIndex));
    }
    function disableGameButtons() {
        $('.game-actions-section').fadeOut();
        $('#twist, #stick, #double-down').prop('disabled', true);
        if (!gameState.gameInProgress) {
            $('.chip-section, .betting-section').fadeIn();
        }
    }
    function forceSyncSideBetDisplays() {
        gameState.players.forEach((player, index) => {
            if (player.sideBets) {
                updatePlayerSideBetDisplay(index);
            }
        });
    }

    function updatePlayerSideBetDisplay(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player && player.sideBets) {
        }
    }

    function toggleFullscreen() {
        if (!document.fullscreenElement) {
            const element = document.documentElement;
            const requestFullscreen = element.requestFullscreen || element.webkitRequestFullscreen;

            requestFullscreen.call(element).then(() => {
            if (screen.orientation && screen.orientation.lock) {
                screen.orientation.lock('portrait').catch(err => {
                console.warn('fail', err);
                });
            }
            }).catch(err => {
            console.log('Fullscreen request failed:', err);
            });
        } else {
            document.exitFullscreen?.();
        }
    }


    function bindEvents() {
        $('#fullscreen-button').on('click', function() {
            toggleFullscreen();
        });
        $('#home-button').on('click', function() {
            window.location.href = '/';
        });
        
        $('#fullscreen-button').on('click', function() {
            toggleFullscreen();
        });

        $('#settings-button').on('click', function() {
            showSettingsModal();
        });
        $('#settings-close').on('click', function() {
            hideSettingsModal();
        });
        $('#rules-button').on('click', function() {
            showRulesModal();
        });
        $('#rules-close').on('click', function() {
            hideRulesModal();
        });
        $('#rules-modal').on('click', function(e) {
            if (e.target === this) {
                hideRulesModal();
            }
        });
        $('#settings-modal').on('click', function(e) {
            if (e.target === this) {
                hideSettingsModal();
            }
        });
        $('.deck-option-btn').on('click', function() {
            $('.deck-option-btn').removeClass('active');
            $(this).addClass('active');
        });
        $('#apply-settings').on('click', function() {
            applySettings();
        });
        $('#restart-game-btn').on('click', function() {
            restartGame();
        });
        $('#game-over-modal').on('click', function(e) {
            if (e.target === this) {
            }
        });
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                if ($('#settings-modal').is(':visible')) {
                    hideSettingsModal();
                } else if ($('#rules-modal').is(':visible')) {
                    hideRulesModal();
                }
            }
        });
        bindChipEvents();
        $('#clear-bet').on('click', function() {
            if (!gameState.gameInProgress && gameState.gameStarted) {
                const currentPlayer = gameState.players[gameState.currentPlayerIndex];
                gameState.balance += currentPlayer.bet;
                currentPlayer.bet = 0;
                updateDisplay();
                updateBetDisplay(gameState.currentPlayerIndex, 0);
                hideDealButton();
                updateGameStatus('Bet cleared - Please place new bet');
                updateChipButtonStates();
                updateButtonStates();
            }
        });

        $('#double-bet').on('click', function() {
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];
            if (!gameState.gameInProgress && currentPlayer.bet > 0 && gameState.gameStarted) {
                const newBetAmount = currentPlayer.bet * 2;
                const additionalBet = newBetAmount - currentPlayer.bet;

                if (gameState.balance >= additionalBet) {
                    gameState.balance -= additionalBet;
                    currentPlayer.bet = newBetAmount;
                    animateChipToBet(additionalBet, gameState.currentPlayerIndex);
                    updateDisplay();
                    safeSetTimeout(() => {
                        updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
                        updateChipButtonStates();
                        updateButtonStates();
                    }, 400);
                    playSound('chipPlace');
                } else {
                    updateGameStatus('Insufficient balance for double bet!');
                }
            }
        });

        $('#deal-cards').on('click', function() {
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];
            if (currentPlayer.bet > 0 && !gameState.gameInProgress && gameState.gameStarted) {
                hideDealButton();
                startNewGame();
            }
        });
        $('.bet-circle').on('click', function() {
            const htmlPosition = parseInt($(this).data('position'));
            let playerIndex = -1;
            for (let i = 0; i < gameSettings.playerCount; i++) {
                if (getPlayerHtmlPosition(i) === htmlPosition) {
                    playerIndex = i;
                    break;
                }
            }
            if (playerIndex === gameState.currentPlayerIndex &&
                gameState.players[playerIndex].bet > 0 &&
                !gameState.gameInProgress &&
                gameState.gamePhase !== 'dealing') {
                startNewGame();
            }
        });
        $('#hint').on('click', showHint);
        $('#twist').on('click', playerTwist);
        $('#stick').on('click', playerStick);
        $('#double-down').on('click', doubleDown);
        $('#split').on('click', function() {
            if (canPlayerSplit(gameState.currentPlayerIndex)) {
                splitPlayerHand(gameState.currentPlayerIndex);
                updateButtonStates();
            }
        });

        $('.side-bet-btn').on('click', function() {
            if (gameState.gameInProgress || !gameState.gameStarted) return;
            const betType = $(this).data('type');
            const betValue = parseInt($(this).data('value'));
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];
            if (gameState.balance >= betValue && betValue > 0) {
                gameState.balance -= betValue;
                currentPlayer.sideBets[betType] += betValue;
                animateChipToSideBet(betValue, gameState.currentPlayerIndex, betType);
                updateDisplay();
                setTimeout(() => {
                    updatePlayerSideBetDisplay(gameState.currentPlayerIndex);
                    $('#perfect-pairs-bet').text(formatBetAmount(currentPlayer.sideBets.perfectPairs));
                    $('#twenty-one-plus-three-bet').text(formatBetAmount(currentPlayer.sideBets.twentyOnePlusThree));
                }, 400);
                $(this).addClass('selected');
                setTimeout(() => $(this).removeClass('selected'), 300);
                playSound('chipPlace');
            } else {
                updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalance') : 'Insufficient balance!');
            }
        });
        $('#clear-side-bets').on('click', function() {
            if (!gameState.gameInProgress && gameState.gameStarted) {
                const currentPlayer = gameState.players[gameState.currentPlayerIndex];
                Object.values(currentPlayer.sideBets).forEach(amount => {
                    gameState.balance += amount;
                });
                currentPlayer.sideBets = {
                    perfectPairs: 0,
                    twentyOnePlusThree: 0
                };
                updateDisplay();
                updatePlayerSideBetDisplay(gameState.currentPlayerIndex);
                $('#perfect-pairs-bet').text('0');
                $('#twenty-one-plus-three-bet').text('0');
                updateGameStatus('Side bets cleared');
            }
        });
        $(document).on('keydown', function(e) {
            if ($('#result-modal').is(':visible')) return;
            switch(e.key.toLowerCase()) {
                case ' ':
                case 'enter':
                    e.preventDefault();
                    if (!gameState.gameInProgress && gameState.currentBet > 0 && gameState.gamePhase !== 'dealing') {
                        startNewGame();
                    }
                    break;
                case 'h':
                case 't':
                    if (gameState.gameInProgress && !$('#twist').prop('disabled')) {
                        playerTwist();
                    }
                    break;
                case 's':
                    if (gameState.gameInProgress && !$('#stick').prop('disabled')) {
                        playerStick();
                    }
                    break;
                case 'd':
                    if (gameState.gameInProgress && !$('#double-down').prop('disabled')) {
                        doubleDown();
                    }
                    break;
                case 'c':
                    $('#clear-bet').click();
                    break;
                case '1':
                    $('.chip-btn[data-value="10000"]').click();
                    break;
                case '2':
                    $('.chip-btn[data-value="20000"]').click();
                    break;
                case '3':
                    $('.chip-btn[data-value="50000"]').click();
                    break;
                case '4':
                    $('.chip-btn[data-value="100000"]').click();
                    break;
                case '5':
                    $('.chip-btn[data-value="250000"]').click();
                    break;
                case '6':
                    $('.chip-btn[data-value="500000"]').click();
                    break;
                case '7':
                    $('.chip-btn[data-value="1000000"]').click();
                    break;
            }
        });
    }
    function initAchievements() {
        loadUserBalance();
        updateDisplay();
    }
    initDOMCache();
    initGame();
    disableGameButtons();
    hideDealButton();
    initAchievements();
    updateButtonStates();
    $('.game-actions-section').hide();
    $('.chip-section, .betting-section').show();
    $('#loading-overlay').fadeOut(1000);

    setTimeout(function() {
        $('#settings-button').off('click').on('click', function() {
            if (!$(this).hasClass('disabled') && !$(this).prop('disabled')) {
                showSettingsModal();
            }
        });

        $('#settings-close').off('click').on('click', function() {
            hideSettingsModal();
        });

        $('#settings-modal').off('click').on('click', function(e) {
            if (e.target === this) {
                hideSettingsModal();
            }
        });

        $('.deck-option-btn').off('click').on('click', function() {
            $('.deck-option-btn').removeClass('active');
            $(this).addClass('active');
        });

        $('#apply-settings').off('click').on('click', function() {
            applySettings();
        });

        updateSettingsButtonState();
    }, 1000);

    $(window).on('beforeunload', function() {
        clearAllTimers();
        cleanupAnimationElements();
    });





    window.resetBalance = resetBalance;
    window.ensureBalanceIntegrity = ensureBalanceIntegrity;
    window.checkCardIntegrity = checkCardIntegrity;
    window.setDebugMode = function(enabled) {
        debugMode = enabled;
        console.log(`Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    };


    function loadUserBalance() {
        const savedBalance = localStorage.getItem('blackjack-balance');
        if (savedBalance) {
            try {
                const balance = parseInt(savedBalance);
                if (balance === 500000) {
                    console.log('Detected old balance value 500000, resetting to 1000');
                    resetBalanceCache();
                    return;
                }
                if (balance > 0) {
                    gameState.balance = balance;
                    gameState.balanceCache = balance;
                    console.log('Loaded cached balance:', balance);
                }
            } catch (error) {
                console.warn('Failed to load cached balance:', error);
            }
        }
    }
    function cacheUserBalance() {
        gameState.balanceCache = gameState.balance;
        localStorage.setItem('blackjack-balance', gameState.balance.toString());
        console.log('Cached user balance:', gameState.balance);
    }
    function resetBalanceCache() {
        gameState.balance = 1000;
        gameState.balanceCache = 1000;
        localStorage.setItem('blackjack-balance', '1000');
        console.log('Reset balance cache to 1000');
    }

    function loadDeckSettings() {
        try {
            const savedDeckCount = localStorage.getItem('pontoon-practice-deck-count');
            if (savedDeckCount) {
                const deckCount = parseInt(savedDeckCount);
                if (deckCount === 2 || deckCount === 6 || deckCount === 8) {
                    gameSettings.deckCount = deckCount;
                    console.log('Loaded deck settings:', deckCount);
                }
            }
        } catch (error) {
            console.warn('Failed to load deck settings:', error);
        }
    }

    function saveDeckSettings() {
        try {
            localStorage.setItem('pontoon-practice-deck-count', gameSettings.deckCount.toString());
            console.log('Saved deck settings:', gameSettings.deckCount);
        } catch (error) {
            console.warn('Failed to save deck settings:', error);
        }
    }

    function canModifySettings() {
        return gameState.gamePhase === 'betting' || gameState.gamePhase === 'waiting' || !gameState.gameStarted;
    }

    function updateSettingsButtonState() {
        const canModify = canModifySettings();
        const $settingsButton = $('#settings-button');

        if (canModify) {
            $settingsButton.removeClass('disabled').prop('disabled', false);
            $settingsButton.attr('title', 'Game Settings');
        } else {
            $settingsButton.addClass('disabled').prop('disabled', true);
            $settingsButton.attr('title', 'Settings can only be changed during betting phase');
        }
    }
    function resetBalance() {
        gameState.balance = 1000;
        updateDisplay();
    }
    function updateDealButtonState() {
        const humanPlayer = gameState.players[gameState.currentPlayerIndex];
        if (humanPlayer.bet > 0 && !gameState.gameInProgress) {
            showDealButton();
            updateGameStatus(window.i18n ? window.i18n.t('messages.clickDoneToStart') : 'Click Done to start dealing cards');
        } else {
            hideDealButton();
            updateGameStatus('Place your bet to start playing');
        }
    }

    function showSettingsModal() {
        if (!canModifySettings()) {
            updateGameStatus('Please modify the settings during the betting phase');
            return;
        }
        updateSettingsDisplay();
        $('#settings-modal').fadeIn(300);
        $('body').css('overflow', 'hidden');
    }

    function hideSettingsModal() {
        $('#settings-modal').fadeOut(300);
        $('body').css('overflow', 'auto');
    }

    function showRulesModal() {
        $('#rules-modal').fadeIn(300);
        $('body').css('overflow', 'hidden');
    }

    function hideRulesModal() {
        $('#rules-modal').fadeOut(300);
        $('body').css('overflow', 'auto');
    }

    function updateSettingsDisplay() {
        const currentDeckText = `${gameSettings.deckCount} Deck${gameSettings.deckCount > 1 ? 's' : ''}`;
        $('#current-deck-count').text(currentDeckText);

        $('.deck-option-btn').removeClass('active');
        const targetDeckCount = gameSettings.pendingDeckCount || gameSettings.deckCount;
        $(`.deck-option-btn[data-decks="${targetDeckCount}"]`).addClass('active');

        if (gameSettings.pendingDeckCount && gameSettings.pendingDeckCount !== gameSettings.deckCount) {
            $('#settings-notice').show();
        } else {
            $('#settings-notice').hide();
        }
    }

    function applySettings() {
        const selectedDecks = parseInt($('.deck-option-btn.active').data('decks'));

        if (selectedDecks !== gameSettings.deckCount) {
            if (canModifySettings()) {
                gameSettings.deckCount = selectedDecks;
                gameSettings.pendingDeckCount = null;
                saveDeckSettings();
                resetGameForNewSettings();
                updateGameStatus(`Game reset with ${selectedDecks} deck${selectedDecks > 1 ? 's' : ''}`);
            } else {
                gameSettings.pendingDeckCount = selectedDecks;
                updateGameStatus('The setting will take effect in the next round');
            }
        }

        hideSettingsModal();
    }

    function resetGameForNewSettings() {
        gameState.players.forEach(player => {
            player.bet = 0;
            player.cards = [];
            player.score = 0;
            player.splitHands = [];
            player.currentSplitIndex = 0;
            player.isBust = false;
            player.hasDoubled = false;
        });

        gameState.gameInProgress = false;
        gameState.gamePhase = 'waiting';
        gameState.currentTurnIndex = -1;
        gameState.dealerCards = [];
        gameState.dealerScore = 0;
        gameState.dealerHiddenCard = null;
        gameState.dealerHasBlackjack = false;

        createDeck();
        shuffleDeck();
        updateDisplay();
        hideAllPlayerScores();

        updateButtonStates();

        clearAllCards();
        startGame();
    }

    function clearAllCards() {
        $('#dealer-cards').empty();
        $('.player-cards-area').empty();
        $('.bet-amount').text('0');
    }

    // British 21 special rules
    function isPontoon(cards) {
        return cards.length === 2 && calculateScore(cards) === 21;
    }

    function isFiveCardTrick(cards) {
        return cards.length === 5 && calculateScore(cards) <= 21;
    }

    function getHandRank(cards) {
        const score = calculateScore(cards);
        
        if (isPontoon(cards)) {
            return { rank: 4, name: 'Pontoon', score: score };
        } else if (isFiveCardTrick(cards)) {
            return { rank: 3, name: 'Five Card Trick', score: score };
        } else if (score === 21) {
            return { rank: 2, name: '21', score: score };
        } else if (score > 21) {
            return { rank: 0, name: 'Bust', score: score };
        } else {
            return { rank: 1, name: 'Regular', score: score };
        }
    }

    function initializePlayers() {
        gameState.players = [];
        const humanPlayerIndex = 0; 
        for (let i = 0; i < gameSettings.playerCount; i++) {
            const isHuman = i === humanPlayerIndex;
            const player = {
                cards: [],
                score: 0,
                bet: 0,
                isActive: false,
                isAI: !isHuman,
                name: 'You',
                isBust: false,
                avatar: 'images/user.png',
                splitHands: [],
                currentHandIndex: 0,
                canSplit: false,
                hasDoubled: false,
            };
            gameState.players.push(player);
        }
        gameState.currentPlayerIndex = humanPlayerIndex;
    }

    function showFiveCardTrickEffect(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.splitHands && player.splitHands.length > 0) {
            const currentHand = player.splitHands[player.currentHandIndex];
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const cardsContainer = $(`#player-cards-${htmlPosition}`);
            const currentHandContainer = cardsContainer.find('.current-hand');
            currentHandContainer.addClass('five-card-trick');
            currentHand.effectClass = 'five-card-trick';
        } else {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            playerPosition.addClass('five-card-trick');
        }
    }

    function updateButtonStates() {
        const gamePhase = gameState.gamePhase;
        const gameInProgress = gameState.gameInProgress;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const hasActiveBet = currentPlayer && currentPlayer.bet > 0;

        updateChipButtonStates();

        const canClearBet = !gameInProgress && hasActiveBet && (gamePhase === 'betting' || gamePhase === 'waiting');
        const canDeal = !gameInProgress && hasActiveBet && (gamePhase === 'betting' || gamePhase === 'waiting');
        const canDoubleBet = !gameInProgress && hasActiveBet && (gamePhase === 'betting' || gamePhase === 'waiting') &&
                           gameState.balance >= currentPlayer.bet;

        $('#clear-bet').prop('disabled', !canClearBet).toggleClass('disabled', !canClearBet);
        $('#double-bet').prop('disabled', !canDoubleBet).toggleClass('disabled', !canDoubleBet);
        $('#deal-cards').prop('disabled', !canDeal).toggleClass('disabled', !canDeal);

        const isPlayerTurn = gamePhase === 'playing' && gameState.currentTurnIndex === gameState.currentPlayerIndex;
        const canHint = gameInProgress || isPlayerTurn;

        let currentScore, currentCards, currentHandBet;
        if (currentPlayer && currentPlayer.splitHands.length > 0) {
            const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
            currentCards = currentHand ? currentHand.cards : [];
            currentScore = currentCards.length > 0 ? calculateScore(currentCards) : 0; 
            currentHandBet = currentHand ? currentHand.bet : 0;
        } else {
            currentCards = currentPlayer ? currentPlayer.cards : [];
            currentScore = currentCards.length > 0 ? calculateScore(currentCards) : 0; 
            currentHandBet = currentPlayer ? currentPlayer.bet : 0;
        }

        const canTwist = isPlayerTurn && currentPlayer && currentScore < 21;

        let canStick = isPlayerTurn && currentPlayer && currentScore >= 15;
        if (currentPlayer && currentPlayer.splitHands.length > 0) {
            const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
            canStick = canStick && currentHand.hasDrawnCard;
        }

        let canDouble = isPlayerTurn && currentPlayer && currentCards.length >= 2 && currentCards.length <= 4 && gameState.balance >= currentHandBet;
        if (currentPlayer && currentPlayer.splitHands.length > 0) {
            const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
            canDouble = canDouble && !currentHand.hasDoubled;
        } else {
            canDouble = canDouble && !currentPlayer.hasDoubled;
        }
        const canSplit = isPlayerTurn && currentPlayer && canPlayerSplit(gameState.currentPlayerIndex);

        if (currentPlayer && currentPlayer.splitHands.length > 0) {
            const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
            console.log('Stick button debug:', {
                isPlayerTurn,
                currentScore,
                storedScore: currentHand ? currentHand.score : 'N/A',
                recalculatedScore: currentCards.length > 0 ? calculateScore(currentCards) : 0,
                canStick,
                gamePhase,
                currentTurnIndex: gameState.currentTurnIndex,
                currentPlayerIndex: gameState.currentPlayerIndex,
                currentHandIndex: currentPlayer.currentHandIndex,
                currentCards: currentCards.map(c => `${c.value}${c.suit}`),
                displayScore: getScoreDisplay(currentCards)
            });
        }

        $('#hint').prop('disabled', !canHint).toggleClass('disabled', !canHint);
        $('#twist').prop('disabled', !canTwist).toggleClass('disabled', !canTwist);
        $('#stick').prop('disabled', !canStick).toggleClass('disabled', !canStick);
        $('#double-down').prop('disabled', !canDouble).toggleClass('disabled', !canDouble);
        $('#split').prop('disabled', !canSplit).toggleClass('disabled', !canSplit);
    }

    function autoFullscreen() {
        if (!document.fullscreenElement &&
            !document.webkitFullscreenElement &&
            !document.mozFullScreenElement &&
            !document.msFullscreenElement) {

            const element = document.documentElement;
            if (element.requestFullscreen) {
                element.requestFullscreen().catch(() => {});
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        }
    }

    // 语言切换功能
    function setupLanguageSwitcher() {
        $('.language-switch-btn').on('click', function() {
            const lang = $(this).data('lang');
            if (window.i18n && typeof window.i18n.changeLanguage === 'function') {
                window.i18n.changeLanguage(lang);
                $('.language-switch-btn').removeClass('active');
                $(this).addClass('active');
            }
        });
    }

    // 初始化语言切换
    setupLanguageSwitcher();

});

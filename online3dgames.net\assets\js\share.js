/**
 * Universal Share Component for Blackjack Games
 * Provides social sharing and copy-to-clipboard functionality
 */

class GameShare {
    constructor() {
        this.gameNames = {
            'blackjack': 'Blackjack 21',
            'blackjack-practice': 'Blackjack Practice',
            'freeBetBlackjack': 'Free Bet Blackjack',
            'pontoon-game': 'Pontoon'
        };
    }

    /**
     * Get current game name from URL
     */
    getCurrentGameName() {
        const path = window.location.pathname;
        for (const [key, name] of Object.entries(this.gameNames)) {
            if (path.includes(key)) {
                return name;
            }
        }
        return 'Blackjack Game';
    }

    /**
     * Share game with custom message
     */
    shareGame(customMessage = null) {
        const gameName = this.getCurrentGameName();
        const gameUrl = window.location.href;
        
        let shareText;
        if (customMessage) {
            shareText = customMessage;
        } else {
            shareText = `Come and play ${gameName} online! 🎰\nExperience the thrill of professional casino gaming.\n${gameUrl}`;
        }

        if (navigator.share) {
            navigator.share({
                title: `${gameName} - Online Casino Game`,
                text: shareText,
                url: gameUrl
            }).catch(err => {
                console.log('Share failed:', err);
                this.copyToClipboard(shareText);
            });
        } else {
            this.copyToClipboard(shareText);
        }
    }

    /**
     * Share game result/score
     */
    shareResult(resultData) {
        const gameName = this.getCurrentGameName();
        const gameUrl = window.location.href;
        
        let shareText = `I just played ${gameName}! 🎰\n`;
        
        if (resultData.balance !== undefined) {
            shareText += `Final Balance: $${resultData.balance}\n`;
        }
        if (resultData.winStreak !== undefined) {
            shareText += `Win Streak: ${resultData.winStreak}\n`;
        }
        if (resultData.handsPlayed !== undefined) {
            shareText += `Hands Played: ${resultData.handsPlayed}\n`;
        }
        
        shareText += `Come and challenge yourself!\n${gameUrl}`;

        if (navigator.share) {
            navigator.share({
                title: `${gameName} - My Game Result`,
                text: shareText,
                url: gameUrl
            }).catch(err => {
                console.log('Share failed:', err);
                this.copyToClipboard(shareText);
            });
        } else {
            this.copyToClipboard(shareText);
        }
    }

    /**
     * Copy text to clipboard
     */
    copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                if (typeof gameSuccess === 'function') {
                    gameSuccess('Game link copied to clipboard!', 'Share Successfully');
                } else {
                    alert('Game link copied to clipboard!');
                }
            }).catch(() => {
                this.fallbackCopyToClipboard(text);
            });
        } else {
            this.fallbackCopyToClipboard(text);
        }
    }

    /**
     * Fallback copy method for older browsers
     */
    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            if (typeof gameSuccess === 'function') {
                gameSuccess('Game link copied to clipboard!', 'Share Successfully');
            } else {
                alert('Game link copied to clipboard!');
            }
        } catch (err) {
            console.error('Copy failed:', err);
            if (typeof gameError === 'function') {
                gameError('Copy failed, please copy manually', 'Share Failed');
            } else {
                alert('Copy failed, please copy manually');
            }
        }
        
        document.body.removeChild(textArea);
    }

    /**
     * Show share modal with options
     */
    showShareModal() {
        const gameName = this.getCurrentGameName();
        const gameUrl = window.location.href;
        
        if (typeof gameModal !== 'undefined') {
            gameModal.custom(
                'Share Game',
                `Share ${gameName} with friends!`,
                [
                    { text: 'Copy Link', type: 'primary', value: 'copy' },
                    { text: 'Share', type: 'secondary', value: 'share' },
                    { text: 'Cancel', type: 'secondary', value: 'cancel' }
                ]
            ).then(result => {
                if (result === 'copy') {
                    this.copyToClipboard(gameUrl);
                } else if (result === 'share') {
                    this.shareGame();
                }
            });
        } else {
            // Fallback if modal system not available
            this.shareGame();
        }
    }
}

// Create global instance
window.gameShare = new GameShare();

// Global convenience functions
window.shareGame = (customMessage) => window.gameShare.shareGame(customMessage);
window.shareGameResult = (resultData) => window.gameShare.shareResult(resultData);
window.showShareModal = () => window.gameShare.showShareModal();
